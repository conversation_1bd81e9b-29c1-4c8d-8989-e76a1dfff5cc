/* Reservation Cards Styling */

.reserved-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 5px;
}

.empty-reservations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    color: #777;
    text-align: center;
}

.empty-reservations i {
    font-size: 32px;
    margin-bottom: 15px;
    color: #555;
}

.empty-reservations p {
    margin: 5px 0;
}

.empty-reservations .hint {
    font-size: 14px;
    color: #666;
}

.reservation-card {
    background-color: #333;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-left: 3px solid var(--main-color);
    transition: transform 0.2s, box-shadow 0.2s;
}

.reservation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #444;
}

.reservation-type {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--highlight-color);
    font-weight: 600;
}

.reservation-type i {
    font-size: 16px;
}

.reservation-actions {
    display: flex;
    gap: 8px;
}

.reservation-actions button {
    background: none;
    border: none;
    color: #aaa;
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.edit-reservation:hover {
    color: #4dabf7;
    background-color: rgba(77, 171, 247, 0.1);
}

.delete-reservation:hover {
    color: #ff6b6b;
    background-color: rgba(255, 107, 107, 0.1);
}

.reservation-route {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 8px;
    background-color: #2a2a2a;
    border-radius: 6px;
}

.route-point {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.point-label {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 3px;
}

.point-value {
    font-weight: 600;
    color: #fff;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.route-arrow {
    margin: 0 10px;
    color: var(--main-color);
}

.reservation-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 12px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #ddd;
    background-color: #3a3a3a;
    padding: 4px 8px;
    border-radius: 4px;
}

.detail-item i {
    color: var(--main-color);
    font-size: 12px;
}

.reservation-notes {
    background-color: #2a2a2a;
    border-radius: 6px;
    padding: 8px 10px;
    margin-bottom: 12px;
    display: flex;
    gap: 8px;
}

.reservation-notes i {
    color: #aaa;
    margin-top: 3px;
}

.reservation-notes p {
    margin: 0;
    font-size: 13px;
    color: #ccc;
    flex: 1;
    line-height: 1.4;
}

.reservation-footer {
    display: flex;
    justify-content: flex-end;
}

.add-to-itinerary-btn {
    background-color: var(--main-color);
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.add-to-itinerary-btn:hover {
    background-color: var(--highlight-color);
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .reservation-route {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .route-arrow {
        transform: rotate(90deg);
        margin: 5px 0;
    }
    
    .point-value {
        max-width: 100%;
    }
    
    .reservation-details {
        flex-direction: column;
        gap: 5px;
    }
}
