<!DOCTYPE html>
<html>

<head>
  <!-- Document metadata -->
  <title>My Profile - Vestigia</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />

  <!-- Core stylesheets -->
  <link rel="stylesheet" href="css/main.css">      <!-- Global styles -->
  <link rel="stylesheet" href="css/navbar.css">    <!-- Navigation bar styles -->
  <link rel="stylesheet" href="css/footer.css">    <!-- Footer styles -->

  <!-- External resources -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"> <!-- Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">      <!-- Additional icons -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Playfair+Display:wght@700;900&display=swap"> <!-- Fonts -->
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" /> <!-- Animation on scroll library -->

  <!-- Favicon -->
  <link rel="icon" href="images/vestigiaLogo.png" type="image/png">

  <style>
    .profile-container {
      max-width: 800px;
      margin: 120px auto 50px;
      padding: 30px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .profile-header {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }

    .profile-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background-color: #861818;
      color: #ffd700;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40px;
      margin-right: 20px;
    }

    .profile-info h1 {
      margin: 0 0 10px 0;
      color: #262626;
    }

    .profile-info p {
      margin: 0;
      color: #666;
    }

    .profile-section {
      margin-bottom: 30px;
    }

    .profile-section h2 {
      color: #861818;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid #ffd700;
    }

    .profile-section p {
      color: #444;
      line-height: 1.6;
    }

    .profile-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .profile-button {
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .primary-button {
      background-color: #861818;
      color: #ffd700;
    }

    .primary-button:hover {
      background-color: #6b1313;
    }

    .secondary-button {
      background-color: #f5f5f5;
      color: #262626;
    }

    .secondary-button:hover {
      background-color: #e0e0e0;
    }

    .not-logged-in {
      text-align: center;
      padding: 50px 20px;
    }

    .not-logged-in h2 {
      color: #861818;
      margin-bottom: 20px;
    }

    .not-logged-in p {
      margin-bottom: 30px;
      color: #444;
    }

    .login-button {
      display: inline-block;
      padding: 12px 30px;
      background-color: #861818;
      color: #ffd700;
      text-decoration: none;
      border-radius: 5px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .login-button:hover {
      background-color: #6b1313;
      transform: translateY(-2px);
    }

    @media (max-width: 768px) {
      .profile-container {
        margin-top: 100px;
        padding: 20px;
      }

      .profile-header {
        flex-direction: column;
        text-align: center;
      }

      .profile-avatar {
        margin-right: 0;
        margin-bottom: 20px;
      }

      .profile-actions {
        flex-direction: column;
      }
    }
  </style>
</head>

<body>
  <!-- Navigation Bar -->
  <div id="navbar-placeholder"></div>

  <!-- Main Content -->
  <div class="profile-container">
    <div id="profile-content">
      <!-- Content will be dynamically loaded based on login status -->
      <div class="not-logged-in">
        <h2>You are not logged in</h2>
        <p>Please log in to view your profile.</p>
        <a href="auth-login.html" class="login-button">Log In</a>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div id="footer-placeholder"></div>

  <!-- JavaScript Resources -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="js/auth.js"></script>
  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
  <script src="js/main.js"></script>
  <script src="js/mobile-menu.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Load navbar and footer
      fetch('navbar.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('navbar-placeholder').innerHTML = data;
          // Initialize auth after navbar is loaded
          if (typeof auth !== 'undefined') {
            auth.init();
          }
        });

      fetch('footer-template.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('footer-placeholder').innerHTML = data;
        });

      // Check if user is logged in
      setTimeout(() => {
        if (auth && auth.isLoggedIn()) {
          const user = auth.user;
          const profileContent = document.getElementById('profile-content');

          // Get first letter of name or email for avatar
          const nameInitial = user.user_metadata && user.user_metadata.full_name
            ? user.user_metadata.full_name.charAt(0).toUpperCase()
            : user.email.charAt(0).toUpperCase();

          // Get display name
          const displayName = user.user_metadata && user.user_metadata.full_name
            ? user.user_metadata.full_name
            : user.email;

          profileContent.innerHTML = `
            <div class="profile-header">
              <div class="profile-avatar">
                ${nameInitial}
              </div>
              <div class="profile-info">
                <h1>${displayName}</h1>
                <p>${user.email}</p>
                <p>Member since ${new Date(user.created_at).toLocaleDateString()}</p>
              </div>
            </div>

            <div class="profile-section">
              <h2>Account Settings</h2>
              <div class="profile-actions">
                <button class="profile-button primary-button" id="change-password-btn">Change Password</button>
                <button class="profile-button secondary-button" id="logout-btn">Logout</button>
              </div>
            </div>

            <div class="profile-section">
              <h2>My Activity</h2>
              <p>Your saved locations, itineraries, and other activity will appear here.</p>
            </div>
          `;

          // Add event listeners to buttons
          document.getElementById('change-password-btn').addEventListener('click', function() {
            // Create password change modal
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            overlay.style.zIndex = '999';
            document.body.appendChild(overlay);

            const popup = document.createElement('div');
            popup.style.position = 'fixed';
            popup.style.top = '50%';
            popup.style.left = '50%';
            popup.style.transform = 'translate(-50%, -50%)';
            popup.style.backgroundColor = '#fff';
            popup.style.padding = '20px';
            popup.style.borderRadius = '10px';
            popup.style.boxShadow = '0px 4px 6px rgba(0, 0, 0, 0.2)';
            popup.style.zIndex = '1000';
            popup.style.width = '400px';
            popup.style.maxWidth = '90%';
            popup.innerHTML = `
              <h2 style="color: #861818; margin-bottom: 20px;">Change Password</h2>
              <div id="password-message" style="margin-bottom: 15px;"></div>
              <div style="margin-bottom: 15px;">
                <label for="new-password" style="display: block; margin-bottom: 5px; text-align: left; color: #444;">New Password</label>
                <input type="password" id="new-password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box;">
              </div>
              <div style="margin-bottom: 20px;">
                <label for="confirm-new-password" style="display: block; margin-bottom: 5px; text-align: left; color: #444;">Confirm New Password</label>
                <input type="password" id="confirm-new-password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box;">
              </div>
              <div style="display: flex; justify-content: flex-end; gap: 10px;">
                <button id="cancel-password-change" style="padding: 10px 20px; background-color: #f5f5f5; color: #444; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
                <button id="save-password-change" style="padding: 10px 20px; background-color: #861818; color: #ffd700; border: none; border-radius: 5px; cursor: pointer;">Save</button>
              </div>
            `;
            document.body.appendChild(popup);

            // Handle cancel button
            document.getElementById('cancel-password-change').addEventListener('click', function() {
              document.body.removeChild(popup);
              document.body.removeChild(overlay);
            });

            // Handle save button
            document.getElementById('save-password-change').addEventListener('click', async function() {
              const newPassword = document.getElementById('new-password').value;
              const confirmNewPassword = document.getElementById('confirm-new-password').value;
              const passwordMessage = document.getElementById('password-message');

              // Validate passwords
              if (!newPassword) {
                passwordMessage.innerHTML = '<div style="color: red;">Please enter a new password</div>';
                return;
              }

              if (newPassword.length < 6) {
                passwordMessage.innerHTML = '<div style="color: red;">Password must be at least 6 characters long</div>';
                return;
              }

              if (newPassword !== confirmNewPassword) {
                passwordMessage.innerHTML = '<div style="color: red;">Passwords do not match</div>';
                return;
              }

              try {
                passwordMessage.innerHTML = '<div style="color: #17a2b8;">Updating password...</div>';

                // Initialize Supabase client
                const supabaseUrl = 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
                // Use the existing Supabase client that was initialized in supabase-client.js

                // Update password
                const { error } = await supabase.auth.updateUser({
                  password: newPassword
                });

                if (error) {
                  throw new Error(error.message);
                }

                passwordMessage.innerHTML = '<div style="color: #28a745;">Password updated successfully!</div>';

                // Close the modal after 2 seconds
                setTimeout(() => {
                  document.body.removeChild(popup);
                  document.body.removeChild(overlay);
                }, 2000);
              } catch (error) {
                passwordMessage.innerHTML = `<div style="color: red;">${error.message || 'Failed to update password'}</div>`;
              }
            });
          });

          document.getElementById('logout-btn').addEventListener('click', function() {
            auth.logout();
            window.location.href = 'index.html';
          });
        }
      }, 500); // Small delay to ensure auth is initialized

      // Initialize AOS
      AOS.init();
    });
  </script>
</body>

</html>
