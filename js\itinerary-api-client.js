/**
 * Itinerary API Client for Vestigia
 *
 * This file provides client-side functions to interact with the Netlify Functions
 * that handle itinerary events and transportation reservations.
 */

// Wrap everything in an IIFE to avoid global variable conflicts
(function() {
  // API base URL configuration for Netlify Functions
  const ITINERARY_API_CONFIG = {
    // Local development URL (when using Netlify CLI)
    development: 'http://localhost:8888/api',

    // Production URL (Netlify Functions)
    production: '/api'
  };

  // Determine environment
  const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
  const ITINERARY_API_BASE_URL = isProduction ? ITINERARY_API_CONFIG.production : ITINERARY_API_CONFIG.development;

  console.log(`Using Itinerary API base URL: ${ITINERARY_API_BASE_URL} (${isProduction ? 'production' : 'development'} mode)`);

  /**
   * Itinerary API Client
 */
  // Define the itineraryAPI object
  window.itineraryAPI = {
    /**
     * Get the base URL for API requests
     * @returns {string} The base URL for API requests
     */
    getBaseUrl: () => ITINERARY_API_BASE_URL,
    /**
     * Save a user event to Supabase
     * @param {Object} event - Event data
     * @returns {Promise<Object>} Saved event
     */
    saveUserEvent: async (event) => {
      try {
        console.log('Saving user event via Netlify Function:', event);
        const response = await fetch(`${ITINERARY_API_BASE_URL}/save-user-event`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(event)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to save user event');
        }

        return await response.json();
      } catch (error) {
        console.error('Error saving user event:', error);
        throw error;
      }
    },

    /**
     * Delete a user event from Supabase
     * @param {string} eventId - Event ID to delete
     * @param {string} userId - User ID
     * @returns {Promise<Object>} Result
     */
    deleteUserEvent: async (eventId, userId) => {
      try {
        console.log(`Deleting user event ${eventId} for user ${userId} via Netlify Function`);
        const response = await fetch(`${ITINERARY_API_BASE_URL}/delete-user-event?event_id=${encodeURIComponent(eventId)}&user_id=${encodeURIComponent(userId)}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete user event');
        }

        return await response.json();
      } catch (error) {
        console.error('Error deleting user event:', error);
        throw error;
      }
    },

    /**
     * Get user events from Supabase
     * @param {string} userId - User ID
     * @returns {Promise<Array>} Array of user events
     */
    getUserEvents: async (userId) => {
      try {
        console.log('Getting user events via Netlify Function for user:', userId);
        const response = await fetch(`${ITINERARY_API_BASE_URL}/get-user-events?user_id=${encodeURIComponent(userId)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to get user events');
        }

        const result = await response.json();
        console.log(`Retrieved ${result.events?.length || 0} events for user ${userId}`);
        return result.events || [];
      } catch (error) {
        console.error('Error getting user events:', error);
        throw error;
      }
    },

    /**
     * Sync user events between local storage and Supabase
     * @param {string} userId - User ID
     * @param {Array} events - Array of event objects to sync
     * @returns {Promise<Object>} Sync results
     */
    syncUserEvents: async (userId, events) => {
      try {
        console.log(`Syncing ${events.length} events for user ${userId} via Netlify Function`);
        console.log('API endpoint:', `${ITINERARY_API_BASE_URL}/sync-user-events`);
        console.log('Request payload:', JSON.stringify({ user_id: userId, events: events.slice(0, 1) }) + (events.length > 1 ? '... (truncated)' : ''));
        
        const response = await fetch(`${ITINERARY_API_BASE_URL}/sync-user-events`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: userId,
            events: events
          })
        });

        console.log('Sync response status:', response.status, response.statusText);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Error response from sync API:', errorData);
          throw new Error(errorData.error || 'Failed to sync user events');
        }

        const responseData = await response.json();
        console.log('Sync successful, response:', responseData);
        return responseData;
      } catch (error) {
        console.error('Error syncing user events:', error);
        throw error;
      }
    },

    /**
     * Save a transportation reservation to Supabase
     * @param {Object} reservation - Reservation data
     * @returns {Promise<Object>} Saved reservation
     */
    saveTransportationReservation: async (reservation) => {
      try {
        console.log('Saving transportation reservation via Netlify Function:', reservation);
        const response = await fetch(`${ITINERARY_API_BASE_URL}/save-transportation-reservation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(reservation)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to save transportation reservation');
        }

        return await response.json();
      } catch (error) {
        console.error('Error saving transportation reservation:', error);
        throw error;
      }
    },

    /**
     * Delete a transportation reservation from Supabase
     * @param {string} userId - User ID
     * @param {string} reservationId - Reservation ID
     * @returns {Promise<Object>} Result
     */
    deleteTransportationReservation: async (userId, reservationId) => {
      try {
        console.log(`Deleting transportation reservation ${reservationId} for user ${userId} via Netlify Function`);
        const response = await fetch(`${ITINERARY_API_BASE_URL}/delete-transportation-reservation?user_id=${encodeURIComponent(userId)}&reservation_id=${encodeURIComponent(reservationId)}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete transportation reservation');
        }

        return await response.json();
      } catch (error) {
        console.error('Error deleting transportation reservation:', error);
        throw error;
      }
    },

    /**
     * Sync transportation reservations between local storage and Supabase
     * @param {string} userId - User ID
     * @param {Array} reservations - Array of reservation objects to sync
     * @returns {Promise<Object>} Sync results
     */
    syncTransportationReservations: async (userId, reservations) => {
      try {
        console.log(`Syncing ${reservations.length} reservations for user ${userId} via Netlify Function`);
        const response = await fetch(`${ITINERARY_API_BASE_URL}/sync-transportation-reservations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: userId,
            reservations: reservations
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to sync transportation reservations');
        }

        return await response.json();
      } catch (error) {
        console.error('Error syncing transportation reservations:', error);
        throw error;
      }
    }
};

  // Simple console log to confirm the API is loaded
  console.log('Itinerary API client loaded and ready to use');
  
})(); // Close the IIFE
