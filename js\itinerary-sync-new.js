/**
 * Itinerary Sync Module for Vestigia
 * Similar to marker sync functionality but for events
 *
 * This module handles syncing events between local storage and Supabase,
 * with duplicate prevention and conflict resolution.
 */

console.log('Loading itinerary sync module...');

/**
 * Save events to local storage
 * @param {Array} events - Array of events to save
 */
function saveEventsToLocalStorage(events) {
  try {
    console.log(`Saving ${events.length} events to localStorage`);
    localStorage.setItem('events', JSON.stringify(events));
    console.log('Events saved to localStorage successfully');
  } catch (error) {
    console.error('Error saving events to localStorage:', error);
  }
}

/**
 * Load events from local storage
 * @returns {Array} Array of events from local storage
 */
function loadEventsFromLocalStorage() {
  try {
    const eventsJson = localStorage.getItem('events');
    const events = JSON.parse(eventsJson) || [];
    console.log(`Loaded ${events.length} events from localStorage`);
    return events;
  } catch (error) {
    console.error('Error loading events from localStorage:', error);
    return [];
  }
}

/**
 * Load events from Supabase
 * @returns {Promise<Array>} Array of events from Supabase
 */
async function loadEventsFromSupabase() {
  try {
    console.log('Loading events from Supabase...');
    const events = await window.itineraryAPI.getUserEvents();
    console.log(`Loaded ${events.length} events from Supabase`);
    return events;
  } catch (error) {
    console.error('Error loading events from Supabase:', error);
    return [];
  }
}

/**
 * Merge events from different sources, removing duplicates
 * @param {Array} localEvents - Events from local storage
 * @param {Array} remoteEvents - Events from Supabase
 * @returns {Array} Merged and deduplicated events
 */
function mergeEvents(localEvents, remoteEvents) {
  console.log(`Merging ${localEvents.length} local events with ${remoteEvents.length} remote events`);

  const eventMap = new Map();

  // Add local events first
  localEvents.forEach(event => {
    if (event && event.id) {
      eventMap.set(event.id, { ...event, source: 'local' });
    }
  });

  // Add remote events, overriding local ones (remote takes precedence)
  remoteEvents.forEach(event => {
    if (event && event.id) {
      eventMap.set(event.id, { ...event, source: 'remote' });
    }
  });

  // Convert back to array and remove source property
  const mergedEvents = Array.from(eventMap.values())
    .map(({ source, ...event }) => event)
    .filter(event => event && event.id);

  console.log(`Merged into ${mergedEvents.length} unique events`);
  return mergedEvents;
}

/**
 * Sync events with Supabase
 * @param {Array} events - Events to sync
 * @returns {Promise<Object>} Sync results
 */
async function syncEventsWithSupabase(events) {
  try {
    console.log(`Syncing ${events.length} events with Supabase`);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('User not authenticated, skipping Supabase sync');
      return { success: false, error: 'Not authenticated' };
    }

    console.log('Syncing events with Supabase for user:', user.id);

    // Use the itineraryAPI client to sync events
    const result = await window.itineraryAPI.syncUserEvents(events);
    console.log('Events synced with Supabase:', result);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error during Supabase sync:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Initialize event sync - load from both sources and merge
 * @returns {Promise<Array>} Merged events
 */
async function initEventSync() {
  console.log('Initializing event sync...');

  try {
    // Load events from local storage
    const localEvents = loadEventsFromLocalStorage();
    console.log(`Loaded ${localEvents.length} events from localStorage`);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    console.log('User authenticated:', isAuthenticated);

    if (!isAuthenticated) {
      // Not authenticated, just use local events
      console.log('User not authenticated, using local events only');
      return localEvents;
    }

    // If authenticated, load and merge remote events
    try {
      console.log('Attempting to load events from Supabase...');
      const remoteEvents = await loadEventsFromSupabase();
      console.log(`Loaded ${remoteEvents.length} events from Supabase`);

      // Merge events
      const mergedEvents = mergeEvents(localEvents, remoteEvents);
      console.log(`Merged into ${mergedEvents.length} total events`);

      // Update local storage with merged events
      saveEventsToLocalStorage(mergedEvents);
      console.log('Saved merged events to local storage');

      // Sync merged events back to Supabase (in case local had new events)
      console.log('Attempting to sync merged events back to Supabase...');
      const syncResult = await syncEventsWithSupabase(mergedEvents);
      console.log('Sync result:', syncResult);

      return mergedEvents;
    } catch (error) {
      console.error('Error during event sync initialization:', error);

      // Fall back to local events if sync fails
      console.log('Falling back to local events due to sync error');
      return localEvents;
    }
  } catch (error) {
    console.error('Error in initEventSync:', error);
    // Return empty array in case of error
    return [];
  }
}

/**
 * Add a new event and sync
 * @param {Object} event - Event to add
 * @returns {Promise<Array>} Updated events array
 */
async function addEvent(event) {
  console.log('Adding new event:', event.title);

  try {
    // Load current events
    let events = loadEventsFromLocalStorage();

    // Add new event
    events.push(event);

    // Save to local storage
    saveEventsToLocalStorage(events);

    // Save to Supabase if authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      console.log('User authenticated, saving event to Supabase...');
      try {
        await window.itineraryAPI.saveUserEvent(event);
        console.log('Event saved to Supabase successfully');
      } catch (error) {
        console.error('Error saving event to Supabase:', error);
        // Continue even if Supabase save fails
      }
    }

    console.log('Event added successfully');
    return events;
  } catch (error) {
    console.error('Error adding event:', error);
    throw error;
  }
}

/**
 * Update an existing event and sync
 * @param {Object} updatedEvent - Updated event data
 * @returns {Promise<Array>} Updated events array
 */
async function updateEvent(updatedEvent) {
  console.log('Updating event:', updatedEvent.id);

  try {
    // Load current events
    let events = loadEventsFromLocalStorage();

    // Update the event
    events = events.map(event =>
      event.id === updatedEvent.id ? updatedEvent : event
    );

    // Save to local storage
    saveEventsToLocalStorage(events);

    // Update in Supabase if authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      console.log('User authenticated, updating event in Supabase...');
      try {
        await window.itineraryAPI.saveUserEvent(updatedEvent);
        console.log('Event updated in Supabase successfully');
      } catch (error) {
        console.error('Error updating event in Supabase:', error);
        // Continue even if Supabase update fails
      }
    }

    console.log('Event updated successfully');
    return events;
  } catch (error) {
    console.error('Error updating event:', error);
    throw error;
  }
}

/**
 * Delete an event and sync
 * @param {string} eventId - ID of event to delete
 * @returns {Promise<Array>} Updated events array
 */
async function deleteEvent(eventId) {
  console.log('Deleting event:', eventId);

  try {
    // Load current events
    let events = loadEventsFromLocalStorage();

    // Remove the event
    events = events.filter(event => event.id !== eventId);

    // Save to local storage
    saveEventsToLocalStorage(events);

    // Delete from Supabase if authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      console.log('User authenticated, deleting from Supabase...');
      try {
        await window.itineraryAPI.deleteUserEvent(eventId);
      } catch (error) {
        console.error('Error deleting from Supabase:', error);
        // Continue even if Supabase deletion fails
      }
    }

    console.log('Event deleted successfully');
    return events;
  } catch (error) {
    console.error('Error deleting event:', error);
    throw error;
  }
}

// Export the sync functions
const itinerarySync = {
  saveEventsToLocalStorage,
  loadEventsFromLocalStorage,
  loadEventsFromSupabase,
  mergeEvents,
  syncEventsWithSupabase,
  initEventSync,
  addEvent,
  updateEvent,
  deleteEvent
};

console.log('Itinerary sync module loaded, setting up itinerarySync object');
window.itinerarySync = itinerarySync;
console.log('itinerarySync object created with methods:', Object.keys(itinerarySync));
