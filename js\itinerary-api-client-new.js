/**
 * Itinerary API Client for Vestigia
 * Similar to map-api-client.js but for events
 *
 * This file provides client-side functions to interact with the Netlify Functions
 * that handle user events in Supabase.
 */

// Determine environment and set API base URL for itinerary
const isItineraryProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
const ITINERARY_API_BASE_URL = isItineraryProduction ?
  '/.netlify/functions' :
  'http://localhost:8888/.netlify/functions';

console.log('Itinerary API Client initialized');
console.log('Environment:', isItineraryProduction ? 'production' : 'development');
console.log('API Base URL:', ITINERARY_API_BASE_URL);

/**
 * Get authentication token from available auth sources
 * @returns {Promise<string|null>} Access token if authenticated, null otherwise
 */
async function getAuthToken() {
  try {
    // First try Supabase auth
    const { data: { session } } = await supabase.auth.getSession();
    if (session?.access_token) {
      console.log('Using Supabase auth token');
      return session.access_token;
    }

    // If Supabase auth fails, check the old auth system
    if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn()) {
      console.log('Using old auth system token');
      const sessionStr = localStorage.getItem('vestigia_session');
      if (sessionStr) {
        const session = JSON.parse(sessionStr);
        // Check if session is not expired
        if (new Date(session.expires_at * 1000) > new Date()) {
          return session.access_token;
        }
      }
    }

    console.log('No valid auth token found');
    return null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
}

/**
 * Itinerary API Client
 */
const itineraryAPI = {
  /**
   * Get all user events from Supabase
   * @returns {Promise<Array>} User's saved events
   */
  getUserEvents: async () => {
    console.log('Fetching user events...');
    try {
      // Get the auth token
      const token = await getAuthToken();
      if (!token) {
        console.log('No auth token found, returning empty events');
        return [];
      }

      console.log('Making request to fetch user events');
      const response = await fetch(`${ITINERARY_API_BASE_URL}/user-events`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response from server:', errorData);
        throw new Error(errorData.error || 'Failed to fetch user events');
      }

      const events = await response.json();
      console.log(`Successfully fetched ${events.length} user events`);
      return events;
    } catch (error) {
      console.error('Error fetching user events:', error);
      throw error;
    }
  },

  /**
   * Save a user event to Supabase
   * @param {Object} event - Event data to save
   * @returns {Promise<Object>} Saved event data
   */
  saveUserEvent: async (event) => {
    console.log('Saving user event:', event);
    try {
      // Get the auth token
      const token = await getAuthToken();
      if (!token) {
        const error = new Error('Not authenticated');
        error.code = 'UNAUTHENTICATED';
        throw error;
      }

      console.log('Sending event to server...');
      const response = await fetch(`${ITINERARY_API_BASE_URL}/user-events`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        },
        body: JSON.stringify(event),
        credentials: 'same-origin'
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response from server:', errorData);
        throw new Error(errorData.error || 'Failed to save user event');
      }

      const savedEvent = await response.json();
      console.log('Event saved successfully:', savedEvent);
      return savedEvent;
    } catch (error) {
      console.error('Error saving user event:', error);
      throw error;
    }
  },

  /**
   * Delete a user event from Supabase
   * @param {string} eventId - Event ID to delete
   * @returns {Promise<boolean>} Success status
   */
  deleteUserEvent: async (eventId) => {
    console.log('Deleting user event:', eventId);
    try {
      // Get the auth token
      const token = await getAuthToken();
      if (!token) {
        const error = new Error('Not authenticated');
        error.code = 'UNAUTHENTICATED';
        throw error;
      }

      const deleteResponse = await fetch(`${ITINERARY_API_BASE_URL}/user-events?id=${encodeURIComponent(eventId)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!deleteResponse.ok) {
        const errorData = await deleteResponse.json();
        throw new Error(errorData.error || 'Failed to delete event');
      }

      console.log('Event deleted successfully:', eventId);
      return true;
    } catch (error) {
      console.error('Error in deleteUserEvent:', {
        message: error.message,
        eventId,
        error: error.stack
      });
      throw error;
    }
  },

  /**
   * Sync multiple events with Supabase
   * @param {Array} events - Array of events to sync
   * @returns {Promise<Object>} Sync results
   */
  syncUserEvents: async (events) => {
    console.log(`Syncing ${events.length} events with Supabase`);
    try {
      // Get the auth token
      const token = await getAuthToken();
      if (!token) {
        const error = new Error('Not authenticated');
        error.code = 'UNAUTHENTICATED';
        throw error;
      }

      // Save each event individually (similar to how markers work)
      const results = {
        saved: [],
        errors: []
      };

      for (const event of events) {
        try {
          const savedEvent = await itineraryAPI.saveUserEvent(event);
          results.saved.push(savedEvent);
        } catch (error) {
          console.error('Error saving event during sync:', error);
          results.errors.push({
            event: event,
            error: error.message
          });
        }
      }

      console.log(`Sync completed: ${results.saved.length} saved, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      console.error('Error syncing user events:', error);
      throw error;
    }
  },

  /**
   * Clean up duplicate events for the current user
   * @returns {Promise<Object>} Cleanup results
   */
  cleanupDuplicateEvents: async () => {
    console.log('Cleaning up duplicate events...');
    try {
      const events = await itineraryAPI.getUserEvents();

      if (!events || events.length === 0) {
        return {
          success: true,
          message: "No events found",
          duplicates_removed: 0
        };
      }

      // Find duplicates based on title, start time, and location
      const uniqueKeys = new Set();
      const duplicates = [];

      events.forEach(event => {
        const key = `${event.title}_${event.start}_${event.location || ''}`;
        if (uniqueKeys.has(key)) {
          duplicates.push(event.id);
        } else {
          uniqueKeys.add(key);
        }
      });

      // Delete duplicates
      if (duplicates.length > 0) {
        for (const eventId of duplicates) {
          try {
            await itineraryAPI.deleteUserEvent(eventId);
          } catch (deleteError) {
            console.error(`Error deleting duplicate event ${eventId}:`, deleteError);
          }
        }

        return {
          success: true,
          message: `Cleaned up ${duplicates.length} duplicate events`,
          duplicates_removed: duplicates.length
        };
      }

      return {
        success: true,
        message: "No duplicates found",
        duplicates_removed: 0
      };
    } catch (error) {
      console.error('Error cleaning up duplicate events:', error);
      return {
        error: error.message,
        success: false,
        duplicates_removed: 0
      };
    }
  }
};

// Export the API client
console.log('Itinerary API client script loaded, setting up itineraryAPI object');
window.itineraryAPI = itineraryAPI;
console.log('itineraryAPI object created with methods:', Object.keys(itineraryAPI));
