<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Events API</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test User Events API</h1>
    <div id="results"></div>
    <button onclick="testAPI()">Test API</button>
    <button onclick="testSaveEvent()">Test Save Event</button>
    <button onclick="testGetEvents()">Test Get Events</button>

    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function log(message) {
            const results = document.getElementById('results');
            results.innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }

        async function testAPI() {
            log('=== Testing User Events API ===');
            
            try {
                // Check auth
                const { data: { session } } = await supabase.auth.getSession();
                log('Auth session: ' + (session ? 'Authenticated' : 'Not authenticated'));
                
                if (!session) {
                    log('Please log in first');
                    return;
                }

                const token = session.access_token;
                log('Token available: ' + !!token);

                // Test the Netlify function
                const response = await fetch('/.netlify/functions/user-events', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log('Response status: ' + response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    log('Response data: ' + JSON.stringify(data, null, 2));
                } else {
                    const errorData = await response.json();
                    log('Error response: ' + JSON.stringify(errorData, null, 2));
                }
            } catch (error) {
                log('Error: ' + error.message);
            }
        }

        async function testSaveEvent() {
            log('=== Testing Save Event ===');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                if (!session) {
                    log('Please log in first');
                    return;
                }

                const testEvent = {
                    id: 'test-event-' + Date.now(),
                    title: 'Test Event ' + new Date().toLocaleTimeString(),
                    start: new Date().toISOString(),
                    end: new Date(Date.now() + 3600000).toISOString(),
                    description: 'This is a test event',
                    location: 'Test Location',
                    category: 'test'
                };

                log('Saving event: ' + JSON.stringify(testEvent, null, 2));

                const response = await fetch('/.netlify/functions/user-events', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${session.access_token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testEvent)
                });

                log('Save response status: ' + response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    log('Save response data: ' + JSON.stringify(data, null, 2));
                } else {
                    const errorData = await response.json();
                    log('Save error response: ' + JSON.stringify(errorData, null, 2));
                }
            } catch (error) {
                log('Save error: ' + error.message);
            }
        }

        async function testGetEvents() {
            log('=== Testing Get Events ===');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                if (!session) {
                    log('Please log in first');
                    return;
                }

                const response = await fetch('/.netlify/functions/user-events', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${session.access_token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log('Get response status: ' + response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    log('Get response data: ' + JSON.stringify(data, null, 2));
                } else {
                    const errorData = await response.json();
                    log('Get error response: ' + JSON.stringify(errorData, null, 2));
                }
            } catch (error) {
                log('Get error: ' + error.message);
            }
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
