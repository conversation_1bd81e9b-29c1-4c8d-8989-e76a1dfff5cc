/**
 * Simple local development server for Vestigia
 * This server serves static files and proxies requests to Netlify Functions
 */

const express = require('express');
const path = require('path');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Amadeus API credentials from .env
const AMADEUS_API_KEY = process.env.AMADEUS_API_KEY;
const AMADEUS_API_SECRET = process.env.AMADEUS_API_SECRET;

// Amadeus API endpoints
const AMADEUS_AUTH_URL = 'https://test.api.amadeus.com/v1/security/oauth2/token';
const AMADEUS_FLIGHT_OFFERS_URL = 'https://test.api.amadeus.com/v2/shopping/flight-offers';
const AMADEUS_LOCATIONS_URL = 'https://test.api.amadeus.com/v1/reference-data/locations';

// Token cache
let tokenCache = {
    token: null,
    expiration: null
};

// Serve static files
app.use(express.static(path.join(__dirname, '.')));

// Function to get Amadeus access token
async function getAmadeusToken() {
    // Check if we have a valid cached token
    if (tokenCache.token && tokenCache.expiration && new Date() < tokenCache.expiration) {
        console.log('Using cached Amadeus token');
        return tokenCache.token;
    }

    try {
        console.log('Requesting new Amadeus token');

        // Request new token
        const response = await axios.post(
            AMADEUS_AUTH_URL,
            `grant_type=client_credentials&client_id=${AMADEUS_API_KEY}&client_secret=${AMADEUS_API_SECRET}`,
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        // Cache the token with expiration (token is valid for 30 minutes, we set it to 29 to be safe)
        tokenCache.token = response.data.access_token;
        tokenCache.expiration = new Date(new Date().getTime() + 29 * 60000);

        return response.data.access_token;
    } catch (error) {
        console.error('Error getting Amadeus token:', error.response?.data || error.message);
        throw error;
    }
}

// Enable CORS for all routes
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
});

// Log all requests for debugging
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// API endpoint for flight search (simulates Netlify Function)
app.get('/api/search-flights', async (req, res) => {
    try {
        // Get access token
        const token = await getAmadeusToken();

        // Extract query parameters
        const { origin, destination, departureDate, returnDate, adults = '1' } = req.query;

        // Validate required parameters
        if (!origin || !destination || !departureDate) {
            return res.status(400).json({
                error: 'Missing required parameters: origin, destination, departureDate'
            });
        }

        // Prepare search parameters
        const searchParams = {
            originLocationCode: origin,
            destinationLocationCode: destination,
            departureDate: departureDate,
            adults: parseInt(adults, 10),
            currencyCode: 'USD',
            max: 20 // Limit results
        };

        // Add return date if provided (for round trips)
        if (returnDate) {
            searchParams.returnDate = returnDate;
        }

        console.log('Searching flights with parameters:', searchParams);

        // Call Amadeus Flight Offers API
        const response = await axios.get(AMADEUS_FLIGHT_OFFERS_URL, {
            headers: {
                'Authorization': `Bearer ${token}`
            },
            params: searchParams
        });

        res.json(response.data);
    } catch (error) {
        console.error('Error searching flights:', error.response?.data || error.message);
        res.status(error.response?.status || 500).json({
            error: error.response?.data?.errors?.[0]?.detail || 'Failed to search flights',
            message: error.message
        });
    }
});

// API endpoint for airport search (simulates Netlify Function)
app.get('/api/search-airports', async (req, res) => {
    try {
        // Get access token
        const token = await getAmadeusToken();

        // Extract query parameters
        const { keyword } = req.query;

        // Validate required parameters
        if (!keyword) {
            return res.status(400).json({
                error: 'Missing required parameter: keyword'
            });
        }

        console.log('Searching airports with keyword:', keyword);

        // Call Amadeus Airport Search API
        const response = await axios.get(AMADEUS_LOCATIONS_URL, {
            headers: {
                'Authorization': `Bearer ${token}`
            },
            params: {
                keyword: keyword,
                subType: 'AIRPORT,CITY',
                'page[limit]': 10
            }
        });

        res.json(response.data);
    } catch (error) {
        console.error('Error searching airports:', error.response?.data || error.message);
        res.status(error.response?.status || 500).json({
            error: error.response?.data?.errors?.[0]?.detail || 'Failed to search airports',
            message: error.message
        });
    }
});

// Default route for the root path
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start the server
app.listen(PORT, () => {
    console.log(`Local development server running on port ${PORT}`);
    console.log(`Open http://localhost:${PORT} in your browser`);
    console.log(`Transportation page: http://localhost:${PORT}/transportation.html`);
});
