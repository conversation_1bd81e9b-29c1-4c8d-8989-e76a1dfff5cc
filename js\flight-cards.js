/**
 * Flight Cards and Airline Booking Functions
 *
 * This file contains functions for creating flight result cards
 * and handling airline booking redirection.
 */


// Function to create a flight card with airline booking redirection
function createFlightCard(flight, formattedDate) {
    // Create stop information
    let stopInfo = '';
    let stopClass = '';

    if (flight.stops === 0) {
        stopInfo = 'Nonstop';
        stopClass = 'nonstop';
    } else if (flight.stops === 1) {
        stopInfo = '1 stop';
        stopClass = 'one-stop';
    } else {
        stopInfo = `${flight.stops} stops`;
        stopClass = 'multi-stop';
    }

    // Get airline website based on carrier code
    const airlineWebsite = getAirlineWebsite(flight.carrierCode);

    // Format amenities for the footer
    const amenitiesHtml = flight.amenities && flight.amenities.length > 0 ?
        flight.amenities.slice(0, 3).map(amenity =>
            `<div class="flight-amenity"><i class="fas fa-${getAmenityIcon(amenity)}"></i> ${amenity}</div>`
        ).join('') : '';

    return `
    <div class="flight-card">
        <div class="flight-card-header">
            <div class="airline-logo">
                <i class="fas fa-plane"></i>
            </div>
            <div class="airline-name">${flight.carrier}</div>
            <div class="flight-number">${flight.flightNumber}</div>
        </div>

        <div class="flight-card-body">
            <div class="flight-route">
                <div class="flight-departure">
                    <div class="flight-time">${flight.departure}</div>
                    <div class="flight-airport">${flight.departureAirport}</div>
                    <div class="flight-date">${formattedDate}</div>
                </div>

                <div class="flight-path">
                    <div class="flight-duration">${flight.duration}</div>
                    <div class="flight-path-line">
                        <div class="flight-path-icon">
                            <i class="fas fa-plane"></i>
                        </div>
                    </div>
                    <div class="flight-stops ${stopClass}">${stopInfo}</div>
                </div>

                <div class="flight-arrival">
                    <div class="flight-time">${flight.arrival}</div>
                    <div class="flight-airport">${flight.arrivalAirport}</div>
                    <div class="flight-date">${formattedDate}</div>
                </div>
            </div>

            <div class="flight-price-section">
                <div class="flight-price">$${flight.price.toFixed(2)}</div>
                <a href="${airlineWebsite}" target="_blank" class="book-btn">
                    <i class="fas fa-external-link-alt"></i> Book Now
                </a>
            </div>
        </div>

        <div class="flight-card-footer">
            <div class="flight-amenities">
                ${amenitiesHtml}
            </div>

            <div class="flight-actions">
                <button class="flight-action-btn select-btn" data-id="${flight.id}">
                    <i class="fas fa-check-circle"></i> Select
                </button>
            </div>
        </div>
    </div>
    `;
}

// Function to create basic cards for trains and buses
function createBasicCard(result) {
    // Create stop information
    let stopInfo = '';
    if (result.stops === 0) {
        stopInfo = '<span class="stops">Nonstop</span>';
    } else {
        const stopLabel = result.stops === 1 ? 'stop' : 'stops';
        stopInfo = `<span class="stops">${result.stops} ${stopLabel}</span>`;
    }

    // Create amenities list
    const amenities = result.amenities ?
        `<div class="amenities">${result.amenities.map(a =>
            `<span class="amenity"><i class="fas fa-check"></i> ${a}</span>`).join('')}</div>` : '';

    // Create carrier logo
    const carrierLogo = result.carrierCode ?
        `<div class="carrier-logo">${result.carrierCode}</div>` :
        `<div class="carrier-logo"><i class="fas fa-${result.type === 'train' ? 'train' : 'bus'}"></i></div>`;

    // Create train/bus number
    const transportNumber = result.trainNumber || result.busNumber || '';
    const transportNumberDisplay = transportNumber ?
        `<div class="flight-number">${transportNumber}</div>` : '';

    return `<div class="result-card ${result.type}">
        <div class="result-left">
            <div class="result-header">
                <div class="carrier">
                    ${carrierLogo}
                    <span class="carrier-name">${result.carrier}</span>
                </div>
                ${transportNumberDisplay}
            </div>
            <div class="result-body">
                <div class="time-info">
                    <div class="time-group">
                        <div class="time">${result.departure}</div>
                        <div class="airport">${result.departureStation}</div>
                    </div>
                    <div class="flight-path">
                        <div class="duration">${result.duration}</div>
                        <div class="path-line"></div>
                        ${stopInfo}
                    </div>
                    <div class="time-group">
                        <div class="time">${result.arrival}</div>
                        <div class="airport">${result.arrivalStation}</div>
                    </div>
                </div>
            </div>
            ${amenities}
        </div>
        <div class="result-right">
            <div class="price-container">
                <div class="price">$${result.price.toFixed(2)}</div>
                <div class="price-subtitle">per person</div>
            </div>
            <button class="book-btn" data-result-id="${result.id}">Select</button>
        </div>
    </div>`;
}

// Helper function to get airline website based on carrier code
function getAirlineWebsite(carrierCode) {
    const airlineWebsites = {
        'DL': 'https://www.delta.com',
        'AA': 'https://www.aa.com',
        'UA': 'https://www.united.com',
        'LH': 'https://www.lufthansa.com',
        'BA': 'https://www.britishairways.com',
        'AF': 'https://www.airfrance.com',
        'KL': 'https://www.klm.com',
        'EK': 'https://www.emirates.com',
        'QR': 'https://www.qatarairways.com',
        'SQ': 'https://www.singaporeair.com',
        'CX': 'https://www.cathaypacific.com',
        'JL': 'https://www.jal.com',
        'NH': 'https://www.ana.co.jp',
        'TK': 'https://www.turkishairlines.com',
        'EY': 'https://www.etihad.com',
        'QF': 'https://www.qantas.com',
        'AC': 'https://www.aircanada.com',
        'WN': 'https://www.southwest.com',
        'B6': 'https://www.jetblue.com',
        'AS': 'https://www.alaskaair.com',
        'FR': 'https://www.ryanair.com',
        'U2': 'https://www.easyjet.com',
        'LX': 'https://www.swiss.com',
        'OS': 'https://www.austrian.com',
        'SN': 'https://www.brusselsairlines.com'
    };

    return airlineWebsites[carrierCode] || 'https://www.google.com/flights';
}

// Helper function to get icon for amenity
function getAmenityIcon(amenity) {
    const amenityIcons = {
        'Wi-Fi': 'wifi',
        'Power outlets': 'plug',
        'In-flight entertainment': 'film',
        'Cafe car': 'coffee',
        'Restroom': 'toilet',
        'Legroom': 'ruler-vertical',
        'Meal': 'utensils'
    };

    return amenityIcons[amenity] || 'check';
}

// Export functions for use in transportation.js
window.flightCards = {
    createFlightCard,
    createBasicCard,
    getAirlineWebsite,
    getAmenityIcon
};
