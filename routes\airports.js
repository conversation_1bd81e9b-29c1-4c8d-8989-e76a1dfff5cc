const express = require('express');
const axios = require('axios');
const router = express.Router();

// Fallback data for when the API is unavailable
const fallbackAirportsData = [
  { code: 'LHR', name: 'Heathrow Airport', city: 'London', country: 'GB', displayName: 'LHR - Heathrow Airport, London' },
  { code: 'LGW', name: 'Gatwick Airport', city: 'London', country: 'GB', displayName: 'LGW - Gatwick Airport, London' },
  { code: 'JFK', name: 'John F. Kennedy International Airport', city: 'New York', country: 'US', displayName: 'JFK - John F. Kennedy International Airport, New York' },
  { code: 'LAX', name: 'Los Angeles International Airport', city: 'Los Angeles', country: 'US', displayName: 'LAX - Los Angeles International Airport, Los Angeles' },
  { code: 'CDG', name: 'Charles <PERSON>le Airport', city: 'Paris', country: 'FR', displayName: 'CDG - Charles de Gaulle Airport, Paris' },
  { code: 'FRA', name: 'Frankfurt Airport', city: 'Frankfurt', country: 'DE', displayName: 'FRA - Frankfurt Airport, Frankfurt' },
  { code: 'AMS', name: 'Amsterdam Airport Schiphol', city: 'Amsterdam', country: 'NL', displayName: 'AMS - Amsterdam Airport Schiphol, Amsterdam' },
  { code: 'MAD', name: 'Adolfo Suárez Madrid–Barajas Airport', city: 'Madrid', country: 'ES', displayName: 'MAD - Adolfo Suárez Madrid–Barajas Airport, Madrid' },
  { code: 'FCO', name: 'Leonardo da Vinci–Fiumicino Airport', city: 'Rome', country: 'IT', displayName: 'FCO - Leonardo da Vinci–Fiumicino Airport, Rome' },
  { code: 'SYD', name: 'Sydney Airport', city: 'Sydney', country: 'AU', displayName: 'SYD - Sydney Airport, Sydney' },
  { code: 'SIN', name: 'Singapore Changi Airport', city: 'Singapore', country: 'SG', displayName: 'SIN - Singapore Changi Airport, Singapore' },
  { code: 'DXB', name: 'Dubai International Airport', city: 'Dubai', country: 'AE', displayName: 'DXB - Dubai International Airport, Dubai' },
  { code: 'HND', name: 'Tokyo Haneda Airport', city: 'Tokyo', country: 'JP', displayName: 'HND - Tokyo Haneda Airport, Tokyo' },
  { code: 'ICN', name: 'Incheon International Airport', city: 'Seoul', country: 'KR', displayName: 'ICN - Incheon International Airport, Seoul' },
  { code: 'YYZ', name: 'Toronto Pearson International Airport', city: 'Toronto', country: 'CA', displayName: 'YYZ - Toronto Pearson International Airport, Toronto' }
];

// Function to get fallback airports based on search query
function getFallbackAirports(query) {
  if (!query) return [];

  const lowerQuery = query.toLowerCase();
  return fallbackAirportsData.filter(airport => {
    return airport.code.toLowerCase().includes(lowerQuery) ||
      airport.name.toLowerCase().includes(lowerQuery) ||
      airport.city.toLowerCase().includes(lowerQuery) ||
      airport.country.toLowerCase().includes(lowerQuery);
  });
}

// Function to get popular fallback airports
function getPopularFallbackAirports() {
  // Return a subset of the fallback data as popular airports
  return fallbackAirportsData.slice(0, 10);
}

/**
 * Search for airports by query
 */
router.get('/search', async (req, res) => {
  console.log('Airport search API called with query:', req.query);
  try {
    const { query } = req.query;

    if (!query) {
      console.log('No query parameter provided');
      return res.status(400).json({ error: 'Query parameter is required' });
    }

    // Log the API key (partially masked for security)
    const apiKey = process.env.AIRPORT_API_TOKEN || 'NOT_SET';
    const maskedKey = apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4);
    console.log(`Using API key: ${maskedKey}`);

    // Make the API request to the airport API
    let response;
    try {
      response = await axios.get(`https://airlabs.co/api/v9/airports?api_key=${process.env.AIRPORT_API_TOKEN}&name=${query}`);

      // Format the response
      console.log('API response received:', response.status);
      console.log('Response data structure:', Object.keys(response.data));

      if (!response.data) {
        console.error('Empty response data');
        return res.status(500).json({ error: 'Empty API response' });
      }

      // Check if the API returned an error
      if (response.data.error) {
        console.error('API returned an error:', response.data.error);
        console.log('Using fallback data for airport search');

        // Use fallback data instead of returning an error
        const fallbackAirports = getFallbackAirports(query);
        return res.json(fallbackAirports);
      }

      if (!response.data.response || !Array.isArray(response.data.response)) {
        console.error('Invalid API response format, response property missing or not an array');
        console.log('Full response data:', response.data);
        console.log('Using fallback data for airport search due to invalid response format');

        // Use fallback data instead of returning an error
        const fallbackAirports = getFallbackAirports(query);
        return res.json(fallbackAirports);
      }
    } catch (apiError) {
      console.error('Error making API request:', apiError.message);
      console.log('Using fallback data for airport search due to API request error');

      // Use fallback data instead of returning an error
      const fallbackAirports = getFallbackAirports(query);
      return res.json(fallbackAirports);
    }

    const airports = response.data.response.map(airport => ({
      code: airport.iata_code || airport.icao_code,
      name: airport.name,
      city: airport.city || '',
      country: airport.country_code || '',
      displayName: `${airport.iata_code || airport.icao_code} - ${airport.name}, ${airport.city || ''}`
    }));

    console.log(`Returning ${airports.length} airports`);
    res.json(airports);
  } catch (error) {
    console.error('Error searching airports:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to search airports',
      details: error.response?.data || error.message
    });
  }
});

/**
 * Get popular airports
 */
router.get('/popular', async (req, res) => {
  console.log('Popular airports API called');
  try {
    // Log the API key (partially masked for security)
    const apiKey = process.env.AIRPORT_API_TOKEN || 'NOT_SET';
    const maskedKey = apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4);
    console.log(`Using API key for popular airports: ${maskedKey}`);

    // Make the API request to get popular airports
    let response;
    try {
      response = await axios.get(`https://airlabs.co/api/v9/airports?api_key=${process.env.AIRPORT_API_TOKEN}&size=20`);

      // Format the response
      console.log('Popular airports API response received:', response.status);
      console.log('Response data structure:', Object.keys(response.data));

      if (!response.data) {
        console.error('Empty response data for popular airports');
        return res.status(500).json({ error: 'Empty API response' });
      }

      // Check if the API returned an error
      if (response.data.error) {
        console.error('API returned an error for popular airports:', response.data.error);
        console.log('Using fallback data for popular airports');

        // Use fallback data instead of returning an error
        const fallbackPopularAirports = getPopularFallbackAirports();
        return res.json(fallbackPopularAirports);
      }

      if (!response.data.response || !Array.isArray(response.data.response)) {
        console.error('Invalid API response format for popular airports, response property missing or not an array');
        console.log('Full response data:', response.data);
        console.log('Using fallback data for popular airports due to invalid response format');

        // Use fallback data instead of returning an error
        const fallbackPopularAirports = getPopularFallbackAirports();
        return res.json(fallbackPopularAirports);
      }
    } catch (apiError) {
      console.error('Error making API request for popular airports:', apiError.message);
      console.log('Using fallback data for popular airports due to API request error');

      // Use fallback data instead of returning an error
      const fallbackPopularAirports = getPopularFallbackAirports();
      return res.json(fallbackPopularAirports);
    }

    const airports = response.data.response
      .filter(airport => airport.iata_code && airport.type === 'large_airport')
      .map(airport => ({
        code: airport.iata_code,
        name: airport.name,
        city: airport.city || '',
        country: airport.country_code || '',
        displayName: `${airport.iata_code} - ${airport.name}, ${airport.city || ''}`
      }));

    console.log(`Returning ${airports.length} popular airports`);
    res.json(airports);
  } catch (error) {
    console.error('Error getting popular airports:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to get popular airports',
      details: error.response?.data || error.message
    });
  }
});

/**
 * Search for airports using Amadeus API
 */
router.get('/search-amadeus', async (req, res) => {
  try {
    const { keyword } = req.query;

    if (!keyword) {
      return res.status(400).json({ error: 'Keyword parameter is required' });
    }

    // Get Amadeus token (reusing the function from flights.js)
    const getAmadeusToken = require('../utils/amadeus').getAmadeusToken;
    const token = await getAmadeusToken();

    // Make the API request
    const response = await axios.get(`https://test.api.amadeus.com/v1/reference-data/locations?subType=AIRPORT&keyword=${keyword}&page[limit]=10`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Format the response
    const airports = response.data.data.map(airport => ({
      code: airport.iataCode,
      name: airport.name,
      city: airport.address.cityName,
      country: airport.address.countryName,
      displayName: `${airport.iataCode} - ${airport.name}, ${airport.address.cityName}`
    }));

    res.json(airports);
  } catch (error) {
    console.error('Error searching airports with Amadeus:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to search airports with Amadeus',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
