// Debug script to help identify issues
document.addEventListener('DOMContentLoaded', function() {
    console.log('Debug script loaded');
    
    // Check if key elements exist
    console.log('Checking key elements:');
    const elements = [
        'transportForm',
        'one-way',
        'round-trip',
        'returnDateContainer',
        'departureInput',
        'destinationInput',
        'departDate',
        'returnDate',
        'passengerDisplay',
        'adultsCount',
        'childrenCount',
        'externalReservationModal',
        'addExternalReservation',
        'externalReservationForm',
        'globeViz'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`${id}: ${element ? 'Found' : 'NOT FOUND'}`);
    });
    
    // Add event listeners to track form submissions
    const transportForm = document.getElementById('transportForm');
    if (transportForm) {
        console.log('Adding event listener to transport form');
        transportForm.addEventListener('submit', function(e) {
            console.log('Transport form submitted');
            console.log('Form data:', {
                departure: document.getElementById('departureInput').value,
                destination: document.getElementById('destinationInput').value,
                departDate: document.getElementById('departDate').value,
                returnDate: document.getElementById('returnDate').value
            });
        });
    }
    
    // Check trip type radio buttons
    const oneWay = document.getElementById('one-way');
    const roundTrip = document.getElementById('round-trip');
    
    if (oneWay && roundTrip) {
        console.log('One-way checked:', oneWay.checked);
        console.log('Round-trip checked:', roundTrip.checked);
        
        oneWay.addEventListener('change', function() {
            console.log('One-way selected');
        });
        
        roundTrip.addEventListener('change', function() {
            console.log('Round-trip selected');
        });
    }
    
    // Check external reservation button
    const addExternalBtn = document.getElementById('addExternalReservation');
    if (addExternalBtn) {
        console.log('Adding event listener to external reservation button');
        addExternalBtn.addEventListener('click', function() {
            console.log('Add external reservation button clicked');
        });
    }
});
