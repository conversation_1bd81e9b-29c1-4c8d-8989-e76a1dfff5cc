-- SQL script to create transportation_reservations table
-- Run this directly in the Supabase SQL Editor

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS transportation_reservations (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  reservation_id TEXT NOT NULL, -- Client-generated ID for syncing
  type TEXT NOT NULL, -- 'flight', 'train', 'bus', etc.
  from_location TEXT NOT NULL,
  to_location TEXT NOT NULL,
  departure_date DATE NOT NULL,
  departure_time TIME,
  carrier TEXT,
  reference TEXT,
  notes TEXT,
  trip_type TEXT DEFAULT 'one-way',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_transportation_reservations_user_id ON transportation_reservations(user_id);
CREATE INDEX IF NOT EXISTS idx_transportation_reservations_reservation_id ON transportation_reservations(reservation_id);
CREATE INDEX IF NOT EXISTS idx_transportation_reservations_departure_date ON transportation_reservations(departure_date);

-- Enable Row Level Security
ALTER TABLE transportation_reservations ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own reservations
DROP POLICY IF EXISTS select_own_reservations ON transportation_reservations;
CREATE POLICY select_own_reservations ON transportation_reservations
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own reservations
DROP POLICY IF EXISTS insert_own_reservations ON transportation_reservations;
CREATE POLICY insert_own_reservations ON transportation_reservations
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own reservations
DROP POLICY IF EXISTS update_own_reservations ON transportation_reservations;
CREATE POLICY update_own_reservations ON transportation_reservations
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own reservations
DROP POLICY IF EXISTS delete_own_reservations ON transportation_reservations;
CREATE POLICY delete_own_reservations ON transportation_reservations
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create policy to allow service role to access all reservations
DROP POLICY IF EXISTS service_role_access_reservations ON transportation_reservations;
CREATE POLICY service_role_access_reservations ON transportation_reservations
  FOR ALL USING (auth.role() = 'service_role');

-- Add policy to allow anonymous role to insert reservations
DROP POLICY IF EXISTS anon_insert_reservations ON transportation_reservations;
CREATE POLICY anon_insert_reservations ON transportation_reservations
  FOR INSERT TO anon
  WITH CHECK (true);

-- Add policy to allow anonymous role to select reservations
DROP POLICY IF EXISTS anon_select_reservations ON transportation_reservations;
CREATE POLICY anon_select_reservations ON transportation_reservations
  FOR SELECT TO anon
  USING (true);

-- Add policy to allow anonymous role to update reservations
DROP POLICY IF EXISTS anon_update_reservations ON transportation_reservations;
CREATE POLICY anon_update_reservations ON transportation_reservations
  FOR UPDATE TO anon
  USING (true);

-- Add policy to allow anonymous role to delete reservations
DROP POLICY IF EXISTS anon_delete_reservations ON transportation_reservations;
CREATE POLICY anon_delete_reservations ON transportation_reservations
  FOR DELETE TO anon
  USING (true);
