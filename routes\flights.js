const express = require('express');
const axios = require('axios');
const router = express.Router();
const { getAmadeusToken } = require('../utils/amadeus');

/**
 * Search for flights using Amadeus API
 */
router.get('/search', async (req, res) => {
  try {
    const { origin, destination, departureDate, returnDate, adults, currency } = req.query;

    // Validate required parameters
    if (!origin || !destination || !departureDate) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Get Amadeus token
    const token = await getAmadeusToken();

    // Build the API URL
    let url = `https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=${origin}&destinationLocationCode=${destination}&departureDate=${departureDate}&adults=${adults || 1}&currencyCode=${currency || 'USD'}`;

    if (returnDate) {
      url += `&returnDate=${returnDate}`;
    }

    // Make the API request
    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    res.json(response.data);
  } catch (error) {
    console.error('Error searching flights:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to search flights',
      details: error.response?.data || error.message
    });
  }
});

/**
 * Search for flights using Kiwi/Tequila API
 */
router.get('/search-kiwi', async (req, res) => {
  try {
    const { fly_from, fly_to, date_from, date_to, limit } = req.query;

    // Validate required parameters
    if (!fly_from || !fly_to || !date_from) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Build the API URL with all query parameters
    const queryParams = new URLSearchParams(req.query).toString();
    const url = `https://tequila-api.kiwi.com/v2/search?${queryParams}`;

    // Make the API request
    const response = await axios.get(url, {
      headers: {
        'apikey': process.env.KIWI_API_KEY
      }
    });

    res.json(response.data);
  } catch (error) {
    console.error('Error searching Kiwi flights:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to search flights with Kiwi',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
