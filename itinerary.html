<!DOCTYPE HTML>
<html>
<head>
    <title>Itinerary - Vestigia</title>
    <meta charset="utf-8" />
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6904623129097601"
     crossorigin="anonymous"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap">
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <link rel="manifest" href="favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#262626">
    <meta name="theme-color" content="#262626">
    <link rel="stylesheet" href="css/itinerary.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.10.2/fullcalendar.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.10.2/fullcalendar.min.js"></script>
    <!-- Load Supabase first -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Add initialization check -->
    <script>
      // Wait for Supabase to be available
      function waitForSupabase() {
        return new Promise((resolve) => {
          if (window.supabase) {
            return resolve();
          }
          const interval = setInterval(() => {
            if (window.supabase) {
              clearInterval(interval);
              resolve();
            }
          }, 100);
        });
      }
    </script>

    <!-- Load other scripts with proper order -->
    <script src="js/supabase-client.js"></script>
    <script>
      // Wait for supabase client to be initialized
      waitForSupabase().then(() => {
        // Now load auth and other scripts
        const scripts = [
          'js/supabase-client.js',
          'js/auth-init.js',
          'js/itinerary-api-client-new.js',
          'js/itinerary-sync-new.js'
        ];

        const loadScript = (src) => {
          return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve();
            script.onerror = (err) => reject(err);
            document.head.appendChild(script);
          });
        };

        // Load scripts in sequence
        scripts.reduce((promise, src) => {
          return promise.then(() => loadScript(src));
        }, Promise.resolve());
      });
    </script>
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/footer.css">

    <script>
      // Test function will be defined after all dependencies are loaded
    </script>
</head>
<body style="font-family: 'Open Sans', sans-serif;">
  <div id="navbar-placeholder"></div>

  <div class="itinerary-container">
    <div class="itinerary-main">
      <section id="calendar-section">
        <h2><span>My Itinerary</span></h2>
        <div id="calendar-controls">
          <select id="month-select"></select>
          <select id="year-select"></select>
        </div>
        <div id="calendar">
          <!-- Calendar will be rendered here -->
        </div>
      </section>

      <!-- Events list section -->
      <section id="events-list-section">
        <h2>Events List</h2>
        <div class="events-controls">
          <div class="events-filter">
            <label for="event-sort">Sort by:</label>
            <select id="event-sort">
              <option value="date-asc" selected>Date (Earliest First)</option>
              <option value="date-desc">Date (Latest First)</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
            </select>
          </div>
          <div class="events-search">
            <i class="fas fa-search"></i>
            <input type="text" id="event-search" placeholder="Search events...">
          </div>
        </div>
        <ul id="events-list">
          <!-- Events will be listed here -->
        </ul>
      </section>
    </div>

    <div class="itinerary-sidebar">
      <!-- Saved places section -->
      <section id="saved-places-section">
        <h2>My Saved Places</h2>
        <div id="saved-places-list">
          <!-- Places will be listed here -->
        </div>
      </section>
    </div>
  </div>

  <!-- Event Modal -->
  <div id="event-modal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2 id="modal-title">Add New Event</h2>
      <form id="event-form">
        <div class="form-group">
          <label for="event-title"><i class="fas fa-heading"></i> Event Title:</label>
          <input type="text" id="event-title" name="event-title" placeholder="Enter event title" required>
        </div>

        <div class="form-group">
          <label for="event-location"><i class="fas fa-map-marker-alt"></i> Location:</label>
          <input list="location-options" id="event-location" name="event-location" placeholder="Enter or select a location">
          <datalist id="location-options">
            <!-- Options will be populated dynamically -->
          </datalist>
        </div>

        <div class="form-row">
          <div class="form-group half">
            <label for="event-start-date"><i class="fas fa-calendar"></i> Start Date:</label>
            <input type="date" id="event-start-date" name="event-start-date" required>
          </div>

          <div class="form-group half">
            <label for="event-start-time"><i class="fas fa-clock"></i> Start Time:</label>
            <input type="time" id="event-start-time" name="event-start-time">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group half">
            <label for="event-end-date"><i class="fas fa-calendar-check"></i> End Date:</label>
            <input type="date" id="event-end-date" name="event-end-date">
          </div>

          <div class="form-group half">
            <label for="event-end-time"><i class="fas fa-hourglass-end"></i> End Time:</label>
            <input type="time" id="event-end-time" name="event-end-time">
          </div>
        </div>

        <div class="form-group">
          <label for="event-category"><i class="fas fa-tag"></i> Category:</label>
          <select id="event-category" name="event-category">
            <option value="cultural">Cultural</option>
            <option value="historical">Historical</option>
            <option value="museum">Museum</option>
            <option value="food">Food & Dining</option>
            <option value="outdoor">Outdoor Activity</option>
            <option value="transportation">Transportation</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div class="form-group">
          <label for="event-info"><i class="fas fa-info-circle"></i> Additional Information:</label>
          <textarea id="event-info" name="event-info" placeholder="Add notes, details, or any other information"></textarea>
        </div>

        <div class="button-container">
          <button type="submit" id="save-button"><i class="fas fa-save"></i> <span>Add Event</span></button>
          <button type="button" id="delete-button" style="display: none;"><i class="fas fa-trash-alt"></i> <span>Delete Event</span></button>
        </div>
      </form>
    </div>
  </div>

  <!-- Event Details Modal -->
  <div id="event-details-modal" class="modal">
    <div class="modal-content event-details-content">
      <span class="close">&times;</span>
      <h2 id="details-modal-title">Event Details</h2>
      <div id="event-details-container">
        <!-- Event details will be populated here -->
        <div class="event-detail-row">
          <div class="detail-label"><i class="fas fa-heading"></i> Title:</div>
          <div id="detail-title" class="detail-value"></div>
        </div>
        <div class="event-detail-row">
          <div class="detail-label"><i class="fas fa-calendar"></i> Date:</div>
          <div id="detail-date" class="detail-value"></div>
        </div>
        <div class="event-detail-row" id="detail-time-row">
          <div class="detail-label"><i class="fas fa-clock"></i> Time:</div>
          <div id="detail-time" class="detail-value"></div>
        </div>
        <div class="event-detail-row" id="detail-location-row">
          <div class="detail-label"><i class="fas fa-map-marker-alt"></i> Location:</div>
          <div id="detail-location" class="detail-value"></div>
        </div>
        <div class="event-detail-row" id="detail-category-row">
          <div class="detail-label"><i class="fas fa-tag"></i> Category:</div>
          <div id="detail-category" class="detail-value"></div>
        </div>
        <div class="event-detail-row" id="detail-info-row">
          <div class="detail-label"><i class="fas fa-info-circle"></i> Additional Information:</div>
          <div id="detail-info" class="detail-value"></div>
        </div>
      </div>
      <div class="details-button-container">
        <button id="edit-event-button" class="secondary-button"><i class="fas fa-edit"></i> <span>Edit Event</span></button>
        <button id="view-on-map-button" class="secondary-button"><i class="fas fa-map"></i> <span>View on Map</span></button>
        <button id="get-directions-button" class="secondary-button"><i class="fas fa-directions"></i> <span>Get Directions</span></button>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div id="footer-placeholder"></div>

  <!-- Include Authentication functionality -->
  <script src="js/auth.js"></script>

  <script>
    // Test if itineraryAPI is available
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Testing itineraryAPI availability...');
      if (typeof window.itineraryAPI === 'undefined') {
        console.error('itineraryAPI not defined on window object after initialization!');
      } else {
        console.log('itineraryAPI is available:', Object.keys(window.itineraryAPI));
      }
    });

    // No need to check for itineraryAPI availability multiple times
    // The script tags above should load the modules correctly
  </script>

  <!-- Include main.js after API and auth -->
  <script src="js/main.js"></script>
  <!-- Mobile menu functionality -->
  <script src="js/mobile-menu.js"></script>

  <script>
    // Back to top button functionality and navbar/footer loading
    document.addEventListener('DOMContentLoaded', function() {
      // Load navbar and footer
      fetch('navbar.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('navbar-placeholder').innerHTML = data;
          // Initialize auth after navbar is loaded
          if (typeof auth !== 'undefined') {
            console.log('Initializing auth after navbar is loaded');
            auth.init();
            // Force a second initialization after a short delay to ensure UI is updated
            setTimeout(() => {
              if (typeof auth !== 'undefined') {
                console.log('Forcing auth UI update after navbar load');
                auth.updateUI(auth.isLoggedIn());
              }
            }, 200);
          }
        });

      fetch('footer-template.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('footer-placeholder').innerHTML = data;
        });

      const backToTopButton = document.getElementById('back-to-top');
      if (backToTopButton) {
        backToTopButton.addEventListener('click', (e) => {
          e.preventDefault();
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }

      // Show/hide back to top button based on scroll position
      window.addEventListener('scroll', () => {
        if (backToTopButton) {
          if (window.scrollY > 300) {
            backToTopButton.style.opacity = '1';
          } else {
            backToTopButton.style.opacity = '0';
          }
        }
      });
    });
  </script>
  <script>
    // Create a simple test function to check if itineraryAPI is available
    function testItineraryAPI() {
      console.log('Testing itineraryAPI availability...');
      if (window.itineraryAPI) {
        console.log('itineraryAPI object exists:', window.itineraryAPI);
        if (typeof window.itineraryAPI.syncUserEvents === 'function') {
          console.log('syncUserEvents function exists and is a function');
        } else {
          console.error('syncUserEvents is not a function or does not exist');
          console.log('Available methods:', Object.keys(window.itineraryAPI));
        }
      } else {
        console.error('itineraryAPI object does not exist');
        // If itineraryAPI doesn't exist, try to recreate it
        console.log('Attempting to recreate itineraryAPI object...');

        // Determine environment
        const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
        const ITINERARY_API_BASE_URL = isProduction ?
          '/.netlify/functions' :
          'http://localhost:8888/.netlify/functions';

        // Define itineraryAPI object
        window.itineraryAPI = {
          syncUserEvents: async (userId, events) => {
            try {
              console.log(`Syncing ${events.length} events for user ${userId} via Netlify Function`);
              const response = await fetch(`${ITINERARY_API_BASE_URL}/sync-user-events`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  user_id: userId,
                  events: events
                })
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to sync user events');
              }

              return await response.json();
            } catch (error) {
              console.error('Error syncing user events:', error);
              throw error;
            }
          }
        };

        console.log('itineraryAPI recreated:', window.itineraryAPI);
      }
    }

    // Run the test after a short delay to ensure scripts are loaded
    setTimeout(testItineraryAPI, 500);
    setTimeout(testItineraryAPI, 1000);
    setTimeout(testItineraryAPI, 2000);

    $(document).ready(function() {
      // Run the test again when document is ready
      testItineraryAPI();

      // And once more after a short delay
      setTimeout(testItineraryAPI, 1000);

      // Check URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const openTransportationModal = urlParams.get('openTransportationModal');
      const openLocationModal = urlParams.get('openLocationModal');

      // We'll handle the openLocationModal parameter after the page is fully loaded
      // Store it for later use
      const shouldOpenLocationModal = openLocationModal === 'true';

      if (openTransportationModal === 'true') {
        // Check if we have pending transportation event data
        const pendingEventData = localStorage.getItem('pendingTransportationEvent');
        if (pendingEventData) {
          try {
            const eventData = JSON.parse(pendingEventData);

            // Pre-fill the form with transportation data
            $('#event-title').val(eventData.title);
            $('#event-location').val(eventData.location);
            $('#event-start-date').val(eventData.startDate);
            $('#event-start-time').val(eventData.startTime);
            $('#event-category').val('transportation');
            $('#event-info').val(eventData.info);

            // Open the modal
            $('#event-modal').css('display', 'block');

            // Update modal title and buttons
            document.querySelector('#modal-title').textContent = 'Add Transportation Event';
            document.querySelector('#save-button span').textContent = 'Add to Calendar';
            document.querySelector('#delete-button').style.display = 'none';

            // Setup form submission handler for new transportation event
            $('#event-form').off('submit').on('submit', function(e) {
              e.preventDefault();

              const title = $('#event-title').val();
              const location = $('#event-location').val();
              const startDate = $('#event-start-date').val();
              const startTime = $('#event-start-time').val();
              const endDate = $('#event-end-date').val();
              const endTime = $('#event-end-time').val();
              const info = $('#event-info').val();
              const category = $('#event-category').val();

              // Validate time inputs
              if (startTime && !endTime && endDate) {
                alert('Please fill in the end time when you specify an end date.');
                return;
              }
              if (!startTime && endTime) {
                alert('Please fill in the start time when you specify an end time.');
                return;
              }

              if (title) {
                // Create new event object
                const eventData = {
                  id: generateEventId(),
                  title: title,
                  start: startTime ? moment(startDate + 'T' + startTime).format() : moment(startDate).format(),
                  end: endDate ? (endTime ? moment(endDate + 'T' + endTime).format() : moment(endDate).format()) : null,
                  description: info,
                  location: location,
                  category: category
                };

                // Add the event to the calendar
                $('#calendar').fullCalendar('renderEvent', eventData, true);

                // Save to local storage
                const events = loadEventsFromLocalStorage();
                events.push(eventData);
                saveEventsToLocalStorage(events); // Save events to local storage after adding

                // Close modal and reset form
                $('#event-modal').css('display', 'none');
                $('#event-form')[0].reset();

                // Unselect the date on the calendar
                $('#calendar').fullCalendar('unselect');
              }
            });

            // Clear the pending event data
            localStorage.removeItem('pendingTransportationEvent');
          } catch (e) {
            console.error('Error parsing pending transportation event:', e);
          }
        }
      }

      const monthSelect = $('#month-select');
      const yearSelect = $('#year-select');
      const currentYear = new Date().getFullYear();
      let currentEvents = [];

      // Initialize month and year selects
      for (let i = 0; i < 12; i++) {
        monthSelect.append(new Option(moment().month(i).format('MMMM'), i));
      }

      for (let i = currentYear - 5; i <= currentYear + 5; i++) {
        yearSelect.append(new Option(i, i));
      }

      monthSelect.val(new Date().getMonth());
      yearSelect.val(currentYear);

      // Fetch location options from local storage
      const userMarkers = JSON.parse(localStorage.getItem('userMarkers')) || [];
      const locationOptions = $('#location-options');
      userMarkers.forEach(function(marker) {
        locationOptions.append(new Option(marker.name, marker.name));
      });

      // Get the existing Supabase client that was initialized in supabase-client.js
      // We don't need to initialize it again here as it's already available globally



      const modal = document.getElementById("event-modal");
      const span = document.getElementsByClassName("close")[0];
      const modalTitle = document.getElementById("modal-title");
      const saveButton = document.getElementById("save-button");
      const deleteButton = document.getElementById("delete-button");
      let currentEditingEvent = null;

      span.onclick = function() {
        modal.style.display = "none";
        resetForm();
      }

      // Remove the window.onclick handler that closes the modal when clicking outside
      // Instead, we'll use mousedown and mouseup events to ensure the modal only closes
      // when both press and release happen outside the modal

      // Track where the mousedown event occurred
      let mouseDownOnModal = false;

      // When mouse is pressed down, check if it's on the modal background
      $(modal).on('mousedown', function(event) {
        // Only track clicks directly on the modal background, not its children
        if (event.target === modal) {
          mouseDownOnModal = true;
        } else {
          mouseDownOnModal = false;
        }
      });

      // When mouse is released, check if we should close the modal
      $(modal).on('mouseup', function(event) {
        // Only close if both mousedown and mouseup occurred on the modal background
        if (mouseDownOnModal && event.target === modal) {
          modal.style.display = "none";
          resetForm();
        }
        // Reset the tracking variable
        mouseDownOnModal = false;
      });

      // Prevent modal from closing when clicking inside the modal content
      $('.modal-content').on('mousedown', function(event) {
        event.stopPropagation();
      });

      // Reset form fields
      function resetForm() {
        $('#event-form')[0].reset();
        currentEditingEvent = null;
      }

      // Event search and filter functionality
      $('#event-search').on('input', function() {
        filterEvents();
      });

      $('#event-sort').on('change', function() {
        filterEvents();
      });

      function filterEvents() {
        const searchTerm = $('#event-search').val().toLowerCase();
        const sortOption = $('#event-sort').val() || 'date-asc'; // Default to date-asc if no option selected
        let filteredEvents = [...currentEvents];

        // Filter by search term
        if (searchTerm) {
          filteredEvents = filteredEvents.filter(event =>
            event.title.toLowerCase().includes(searchTerm) ||
            (event.location && event.location.toLowerCase().includes(searchTerm)) ||
            (event.description && event.description.toLowerCase().includes(searchTerm))
          );
        }

        // Sort events
        switch(sortOption) {
          case 'date-asc':
            filteredEvents.sort((a, b) => {
              // Ensure we have valid dates by parsing with moment
              const dateA = moment(a.start);
              const dateB = moment(b.start);

              // Check if dates are valid before comparing
              if (!dateA.isValid()) return 1;  // Invalid dates go to the end
              if (!dateB.isValid()) return -1; // Invalid dates go to the end

              return dateA.valueOf() - dateB.valueOf();
            });
            break;
          case 'date-desc':
            filteredEvents.sort((a, b) => {
              // Ensure we have valid dates by parsing with moment
              const dateA = moment(a.start);
              const dateB = moment(b.start);

              // Check if dates are valid before comparing
              if (!dateA.isValid()) return 1;  // Invalid dates go to the end
              if (!dateB.isValid()) return -1; // Invalid dates go to the end

              return dateB.valueOf() - dateA.valueOf();
            });
            break;
          case 'name-asc':
            filteredEvents.sort((a, b) => (a.title || '').localeCompare(b.title || ''));
            break;
          case 'name-desc':
            filteredEvents.sort((a, b) => (b.title || '').localeCompare(a.title || ''));
            break;
        }

        renderEventsList(filteredEvents);
      }

      // Save events to local storage and Supabase if user is logged in
      async function saveEventsToLocalStorage(events) {
        console.log('Saving events to localStorage:', events);
        try {
          // Use the itinerarySync module to save events
          if (window.itinerarySync && typeof window.itinerarySync.saveEventsToLocalStorage === 'function') {
            window.itinerarySync.saveEventsToLocalStorage(events);
            console.log('Events saved using itinerarySync module');
          } else {
            // Fallback to direct localStorage if the module isn't available
            localStorage.setItem('events', JSON.stringify(events));
            console.log('Events saved directly to localStorage (fallback)');
          }

          currentEvents = events;
          renderEventsList(events);
          markDaysWithEvents();
          console.log('Events saved successfully. Current count:', events.length);

          // Sync with Supabase in the background if the user is logged in
          const { data: { user } } = await supabase.auth.getUser();
          if (user) {
            console.log('User is logged in, syncing with Supabase...');
            if (window.itinerarySync && typeof window.itinerarySync.syncEventsWithSupabase === 'function') {
              // Use the itinerarySync module
              window.itinerarySync.syncEventsWithSupabase(events).then(result => {
                if (result.success) {
                  console.log('Events synced with Supabase successfully');
                } else {
                  console.warn('Supabase sync completed with warnings:', result.error);
                }
              }).catch(error => {
                console.error('Error during background Supabase sync:', error);
              });
            } else {
              // Fallback to the old method
              syncEventsWithSupabase(events).then(result => {
                if (result.success) {
                  console.log('Events synced with Supabase successfully (fallback)');
                } else {
                  console.warn('Supabase sync completed with warnings:', result.error);
                }
              }).catch(error => {
                console.error('Error during background Supabase sync:', error);
              });
            }
          } else {
            console.log('User not logged in, skipping Supabase sync');
          }
        } catch (error) {
          console.error('Error saving events:', error);
        }
      }

      // Generate a unique ID for each event
      function generateEventId() {
        return 'event-' + new Date().getTime();
      }

      // Load events from local storage and Supabase if user is logged in
      async function loadEventsFromLocalStorage() {
        console.log('Loading events from localStorage and/or Supabase');
        let events = [];

        try {
          // Check if we can use the itinerarySync module
          if (window.itinerarySync && typeof window.itinerarySync.initEventSync === 'function') {
            console.log('Using itinerarySync module to load and sync events');
            // The initEventSync function will handle loading from both localStorage and Supabase
            // It will also handle merging and syncing back to Supabase if needed
            events = await window.itinerarySync.initEventSync();
            console.log(`Loaded ${events.length} events using itinerarySync module`);
          } else {
            // Fallback to the original implementation
            console.log('Fallback: Loading events directly from localStorage');
            // First, load from local storage
            try {
              const eventsJson = localStorage.getItem('events');
              console.log('Raw events from localStorage:', eventsJson);
              events = JSON.parse(eventsJson) || [];
              console.log('Parsed events from localStorage:', events);
            } catch (error) {
              console.error('Error loading events from localStorage:', error);
            }

            // Then try to load from Supabase if user is logged in
            try {
              if (typeof supabase === 'undefined' || !supabase) {
                console.log('Supabase not available, skipping remote sync');
                return events;
              }
              const { data: { user } } = await supabase.auth.getUser();
              if (user) {
                console.log('User is logged in, loading events from Supabase...');
                const remoteEvents = await loadEventsFromSupabase();

                if (remoteEvents.length > 0) {
                  console.log(`Loaded ${remoteEvents.length} events from Supabase`);
                  // Merge remote events with local ones (remote takes precedence)
                  const mergedEvents = mergeEvents(events, remoteEvents);

                  // If we got new events, save them to local storage
                  if (mergedEvents.length > events.length) {
                    console.log('Saving merged events to local storage');
                    localStorage.setItem('events', JSON.stringify(mergedEvents));
                    events = mergedEvents;
                  }
                } else {
                  console.log('No events found in Supabase');
                }
              } else {
                console.log('User is not logged in, using local storage only');
              }
            } catch (error) {
              console.error('Error loading from Supabase, using local storage only:', error);
            }
          }

          // Sort events by date (earliest first) by default
          events.sort((a, b) => {
            const dateA = moment(a.start);
            const dateB = moment(b.start);

            // Check if dates are valid before comparing
            if (!dateA.isValid()) return 1;  // Invalid dates go to the end
            if (!dateB.isValid()) return -1; // Invalid dates go to the end

            return dateA.valueOf() - dateB.valueOf();
          });

          currentEvents = events;
          renderEventsList(events);
          markDaysWithEvents();
          console.log('Events loaded and sorted. Current count:', events.length);
          return events;
        } catch (error) {
          console.error('Error in loadEventsFromLocalStorage:', error);
          // Return empty array in case of error
          return [];
        }
      }

      // Mark days that have events
      function markDaysWithEvents() {
        // Remove existing markers
        $('.fc-day').removeClass('has-events');

        // Add markers to days with events
        currentEvents.forEach(event => {
          const eventDate = moment(event.start).format('YYYY-MM-DD');
          $(`.fc-day[data-date="${eventDate}"]`).addClass('has-events');
        });
      }

      // Render the events list
      function renderEventsList(events) {
        const eventsList = $('#events-list');
        eventsList.empty();

        if (events.length === 0) {
          eventsList.append('<li class="no-events">No events found. Add an event by clicking on a date in the calendar.</li>');
          return;
        }

        events.forEach(event => {
          const listItem = $('<li></li>');

          // Event header with title and date
          const eventHeader = $('<div class="event-header"></div>');
          const eventTitle = $('<h3 class="event-title"></h3>').text(event.title);
          const eventDate = $('<div class="event-date"></div>').text(
            `${moment(event.start).format('ddd, MMM D, YYYY')}${event.start.includes('T') ? ' • ' + moment(event.start).format('h:mm A') : ''}`
          );
          eventHeader.append(eventTitle, eventDate);

          // Make the event header clickable to open details modal
          eventHeader.css('cursor', 'pointer');
          eventHeader.on('click', function() {
            openEventDetailsModal(event);
          });

          // Event location if available
          if (event.location) {
            const eventLocation = $('<div class="event-location"></div>')
              .append('<i class="fas fa-map-marker-alt"></i>')
              .append($('<span></span>').text(event.location));
            listItem.append(eventLocation);

            // Make location clickable to open details modal
            eventLocation.css('cursor', 'pointer');
            eventLocation.on('click', function() {
              openEventDetailsModal(event);
            });
          }

          // Event description if available
          if (event.description) {
            const eventDescription = $('<div class="event-description"></div>').text(
              event.description.length > 100 ? event.description.substring(0, 100) + '...' : event.description
            );
            listItem.append(eventDescription);

            // Make description clickable to open details modal
            eventDescription.css('cursor', 'pointer');
            eventDescription.on('click', function() {
              openEventDetailsModal(event);
            });
          }

          // Button container
          const buttonContainer = $('<div class="button-container"></div>');

          // View Details button
          const viewDetailsButton = $('<button></button>')
            .append('<i class="fas fa-info-circle"></i>')
            .append('<span>View Details</span>')
            .click(() => {
              openEventDetailsModal(event);
            });

          // View/Edit Event button
          const viewEventButton = $('<button></button>')
            .append('<i class="fas fa-edit"></i>')
            .append('<span>Edit</span>')
            .click(() => {
              // Navigate calendar to the event's date
              $('#calendar').fullCalendar('gotoDate', moment(event.start));

              // Highlight the event on the calendar
              $('#calendar').fullCalendar('unselect');
              setTimeout(() => {
                const eventElement = $(`[data-event-id="${event.id}"]`);
                if (eventElement.length) {
                  eventElement.css('background-color', '#ffd700').delay(2000).queue(function(next) {
                    $(this).css('background-color', '');
                    next();
                  });
                }
              }, 100);

              // Open modal with event details
              openEditEventModal(event);

              // Scroll to calendar
              document.getElementById('calendar-section').scrollIntoView({ behavior: 'smooth' });
            });

          // Directions button
          const directionsButton = $('<button></button>')
            .append('<i class="fas fa-directions"></i>')
            .append('<span>Directions</span>')
            .click(() => {
              window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(event.location)}`, '_blank');
            });

          // View on Map button
          const locationButton = $('<button></button>')
            .append('<i class="fas fa-map"></i>')
            .append('<span>Map</span>')
            .click(() => {
              window.location.href = `map.html?location=${encodeURIComponent(event.location)}`;
            });

          // Show on Calendar button
          const showOnCalendarBtn = $('<button></button>')
            .append('<i class="fas fa-calendar-day"></i>')
            .append('<span>Show on Calendar</span>')
            .click(() => {
              // Scroll to calendar first - this ensures we scroll before doing anything else
              document.getElementById('calendar-section').scrollIntoView({ behavior: 'smooth' });

              // Navigate calendar to the event's date
              const eventDate = moment(event.start);
              $('#calendar').fullCalendar('gotoDate', eventDate);

              // Update month and year selectors to match the event date
              monthSelect.val(eventDate.month());
              yearSelect.val(eventDate.year());

              // Highlight the event on the calendar
              $('#calendar').fullCalendar('unselect');
              setTimeout(() => {
                const eventElement = $(`[data-event-id="${event.id}"]`);
                if (eventElement.length) {
                  // Add a pulsing highlight effect
                  eventElement.css({
                    'background-color': '#ffd700',
                    'color': '#333',
                    'box-shadow': '0 0 15px rgba(255, 215, 0, 0.7)'
                  }).addClass('pulse-highlight');

                  // Remove the highlight after 3 seconds
                  setTimeout(() => {
                    eventElement.css({
                      'background-color': '',
                      'color': '',
                      'box-shadow': ''
                    }).removeClass('pulse-highlight');
                  }, 3000);
                }
              }, 300);
            });

          // Add all buttons based on what's available
          if (event.location) {
            buttonContainer.append(viewDetailsButton, showOnCalendarBtn, viewEventButton, directionsButton, locationButton);
          } else {
            buttonContainer.append(viewDetailsButton, showOnCalendarBtn, viewEventButton);
          }

          listItem.prepend(eventHeader);
          listItem.append(buttonContainer);
          eventsList.append(listItem);
        });
      }

      // Initialize calendar with empty events first, then load events asynchronously
      let calendarEvents = [];
      console.log('Initializing calendar with empty events first');

      // Function to initialize the calendar
      function initializeCalendar() {
        console.log('Initializing calendar...');
        $('#calendar').fullCalendar({
        editable: true,
        events: calendarEvents,
        selectable: true,
        selectHelper: true,
        eventOrder: 'start', // Sort events by start date
        eventRender: function(event, element) {
          console.log('Event rendered:', event);
          // Add data attribute for easier event identification
          element.attr('data-event-id', event.id);
        },
        header: {
          left: '',
          center: '',
          right: ''
        },
        // Add a callback for when the calendar is fully rendered
        viewRender: function() {
          // Check if we should open the location modal (from map.html)
          if (shouldOpenLocationModal) {
            // Get the location from localStorage
            const locationName = localStorage.getItem('pendingItineraryLocation');
            if (locationName) {
              console.log('Retrieved location from localStorage:', locationName);
              // Reset form and set defaults
              resetForm();

              // Update modal title and buttons
              modalTitle.textContent = "Add New Event";
              saveButton.querySelector('span').textContent = "Add Event";
              deleteButton.style.display = "none";

              // Set the location from localStorage - no need to decode as localStorage already stores the decoded value
              $('#event-location').val(locationName);
              // Also set the title to the location name as a default
              $('#event-title').val('Visit ' + locationName);

              // Set today's date as default
              const today = moment().format('YYYY-MM-DD');
              $('#event-start-date').val(today);

              // Show modal after a short delay to ensure everything is ready
              setTimeout(() => {
                modal.style.display = "block";
                // Focus on the title field
                $('#event-title').focus();
              }, 500);

              // Setup form submission handler for adding a new event from saved location
              $('#event-form').off('submit').on('submit', function(e) {
                e.preventDefault();
                const title = $('#event-title').val();
                const location = $('#event-location').val();
                const startDate = $('#event-start-date').val();
                const startTime = $('#event-start-time').val();
                const endDate = $('#event-end-date').val();
                const endTime = $('#event-end-time').val();
                const info = $('#event-info').val();
                const category = $('#event-category').val();

                // Validate time inputs
                if (startTime && !endTime && endDate) {
                  alert('Please fill in the end time when you specify an end date.');
                  return;
                }
                if (!startTime && endTime) {
                  alert('Please fill in the start time when you specify an end time.');
                  return;
                }

                if (title) {
                  // Create new event object
                  const eventData = {
                    id: generateEventId(),
                    title: title,
                    start: startTime ? moment(startDate + 'T' + startTime).format() : moment(startDate).format(),
                    end: endDate ? (endTime ? moment(endDate + 'T' + endTime).format() : moment(endDate).format()) : null,
                    description: info,
                    location: location,
                    category: category
                  };

                  // Add the event to the calendar
                  $('#calendar').fullCalendar('renderEvent', eventData, true);
                  console.log('Event rendered on calendar from saved location:', eventData);

                  // Save to local storage and sync with Supabase
                  if (window.itinerarySync && typeof window.itinerarySync.addEvent === 'function') {
                    // Use the itinerarySync module to add the event
                    window.itinerarySync.addEvent(eventData).then(updatedEvents => {
                      console.log('Event added using itinerarySync module:', eventData);
                      // Update the calendar with the updated events
                      calendarEvents = updatedEvents;
                      $('#calendar').fullCalendar('removeEvents');
                      $('#calendar').fullCalendar('addEventSource', updatedEvents);
                      $('#calendar').fullCalendar('refetchEvents');
                      markDaysWithEvents();
                    }).catch(error => {
                      console.error('Error adding event with itinerarySync:', error);
                      // Fallback to the original method
                      loadEventsFromLocalStorage().then(events => {
                        events.push(eventData);
                        saveEventsToLocalStorage(events); // Save events to local storage after adding
                      }).catch(fallbackError => {
                        console.error('Error in fallback event adding:', fallbackError);
                        // Fallback to just saving the new event
                        saveEventsToLocalStorage([eventData]);
                      });
                    });
                  } else {
                    // Fallback to the original method
                    loadEventsFromLocalStorage().then(events => {
                      events.push(eventData);
                      saveEventsToLocalStorage(events); // Save events to local storage after adding
                    }).catch(error => {
                      console.error('Error loading events:', error);
                      // Fallback to just saving the new event
                      saveEventsToLocalStorage([eventData]);
                    });
                  }

                  // Refresh the calendar to ensure the event is displayed
                  $('#calendar').fullCalendar('refetchEvents');

                  // Close modal and reset form
                  modal.style.display = "none";
                  $('#event-form')[0].reset();

                  // Unselect the date on the calendar
                  $('#calendar').fullCalendar('unselect');

                  // Navigate to the event date
                  $('#calendar').fullCalendar('gotoDate', moment(eventData.start));
                }
              });

              // Clear the pending location from localStorage
              localStorage.removeItem('pendingItineraryLocation');
            }
          }
        },
        select: function(start, end) {
          // Reset form and set defaults
          resetForm();

          // Update modal title and buttons
          modalTitle.textContent = "Add New Event";
          saveButton.querySelector('span').textContent = "Add Event";
          deleteButton.style.display = "none";

          // Set the selected date
          $('#event-start-date').val(moment(start).format('YYYY-MM-DD'));

          // Show modal
          modal.style.display = "block";
          // Setup form submission for adding a new event
          $('#event-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            const title = $('#event-title').val();
            const location = $('#event-location').val();
            const startDate = $('#event-start-date').val();
            const startTime = $('#event-start-time').val();
            const endDate = $('#event-end-date').val();
            const endTime = $('#event-end-time').val();
            const info = $('#event-info').val();
            const category = $('#event-category').val();

            // Validate time inputs
            if (startTime && !endTime && endDate) {
              alert('Please fill in the end time when you specify an end date.');
              return;
            }
            if (!startTime && endTime) {
              alert('Please fill in the start time when you specify an end time.');
              return;
            }

            if (title) {
              // Create new event object
              const eventData = {
                id: generateEventId(),
                title: title,
                start: startTime ? moment(startDate + 'T' + startTime).format() : moment(startDate).format(),
                end: endDate ? (endTime ? moment(endDate + 'T' + endTime).format() : moment(endDate).format()) : null,
                description: info,
                location: location,
                category: category
              };

              // Add the event to the calendar
              $('#calendar').fullCalendar('renderEvent', eventData, true);
              console.log('Event rendered on calendar from date selection:', eventData);

              // Save using the new itinerary sync system
              if (window.itinerarySync && typeof window.itinerarySync.addEvent === 'function') {
                console.log('Using itinerarySync module to add event:', eventData);
                window.itinerarySync.addEvent(eventData).then(updatedEvents => {
                  console.log('Event added using itinerarySync module');
                  // Update the calendar with the updated events
                  calendarEvents = updatedEvents;
                  $('#calendar').fullCalendar('removeEvents');
                  $('#calendar').fullCalendar('addEventSource', updatedEvents);
                  $('#calendar').fullCalendar('refetchEvents');
                  markDaysWithEvents();
                }).catch(error => {
                  console.error('Error adding event with itinerarySync:', error);
                  // Fallback to the original method
                  loadEventsFromLocalStorage().then(events => {
                    events.push(eventData);
                    saveEventsToLocalStorage(events);
                  }).catch(fallbackError => {
                    console.error('Error in fallback event adding:', fallbackError);
                    saveEventsToLocalStorage([eventData]);
                  });
                });
              } else {
                console.log('itinerarySync not available, using fallback method');
                // Fallback to the original method
                loadEventsFromLocalStorage().then(events => {
                  events.push(eventData);
                  saveEventsToLocalStorage(events);
                }).catch(error => {
                  console.error('Error loading events:', error);
                  saveEventsToLocalStorage([eventData]);
                });
              }

              // Refresh the calendar to ensure the event is displayed
              $('#calendar').fullCalendar('refetchEvents');

              // Close modal and reset form
              modal.style.display = "none";
              $('#event-form')[0].reset();

              // Navigate to the event date
              $('#calendar').fullCalendar('gotoDate', moment(eventData.start));
            }

            // Unselect the date on the calendar
            $('#calendar').fullCalendar('unselect');
          });
        },
        eventClick: function(event) {
          // Open the event details modal
          openEventDetailsModal(event);
        }
      });

      monthSelect.change(function() {
        const selectedMonth = $(this).val();
        const selectedYear = yearSelect.val();
        $('#calendar').fullCalendar('gotoDate', new Date(selectedYear, selectedMonth));
      });

      yearSelect.change(function() {
        const selectedYear = $(this).val();
        const selectedMonth = monthSelect.val();
        $('#calendar').fullCalendar('gotoDate', new Date(selectedYear, selectedMonth));
      });

      // Load events after calendar is initialized
      loadEventsFromLocalStorage().then(events => {
        console.log('Loaded events asynchronously:', events);
        // Add events to calendar
        $('#calendar').fullCalendar('removeEvents');
        $('#calendar').fullCalendar('addEventSource', events);
        $('#calendar').fullCalendar('refetchEvents');
        markDaysWithEvents();
      }).catch(error => {
        console.error('Error loading events asynchronously:', error);
      });

      // Add these new functions to handle saved places
      function loadSavedPlaces() {
        const markers = JSON.parse(localStorage.getItem('userMarkers')) || [];
        const savedPlacesList = $('#saved-places-list');
        savedPlacesList.empty();

        if (markers.length === 0) {
          savedPlacesList.append('<p class="no-places">No places saved yet. Add places from the Map page.</p>');
          return;
        }

        const placesList = $('<ul></ul>').addClass('saved-places');
        markers.forEach(marker => {
          const listItem = $('<li></li>').addClass('place-item');
          const placeInfo = $('<div></div>').addClass('place-info');

          // Place header with name
          placeInfo.append($('<h3></h3>').text(marker.name));

          // Place description if available
          if (marker.placeName || marker.description) {
            placeInfo.append($('<p></p>').text(marker.placeName || marker.description));
          }

          // Place coordinates
          if (marker.lat && marker.lng) {
            const coordsText = `${marker.lat.toFixed(5)}, ${marker.lng.toFixed(5)}`;
            const coordsElement = $('<div class="place-coords"></div>')
              .append('<i class="fas fa-map-pin"></i>')
              .append($('<span></span>').text(coordsText));
            placeInfo.append(coordsElement);
          }

          const buttonContainer = $('<div></div>').addClass('place-actions');

          // Add to Calendar button
          const addToCalendarBtn = $('<button></button>')
            .append('<i class="fas fa-calendar-plus"></i>')
            .append('<span>Add to Calendar</span>')
            .addClass('add-to-calendar-btn')
            .click(() => {
              // Prefill the event form with place details
              $('#event-title').val(marker.name);
              $('#event-location').val(marker.name);
              $('#event-category').val(marker.category || 'cultural');
              $('#event-info').val(marker.description || '');

              // Set today's date as default
              const today = moment().format('YYYY-MM-DD');
              $('#event-start-date').val(today);

              // Open the modal
              modalTitle.textContent = "Add Event from Saved Place";
              saveButton.querySelector('span').textContent = "Add Event";
              deleteButton.style.display = "none";
              modal.style.display = "block";

              // Setup form submission handler for adding a saved location to the calendar
              $('#event-form').off('submit').on('submit', function(e) {
                e.preventDefault();
                console.log('Saved location form submitted');

                const title = $('#event-title').val();
                const location = $('#event-location').val();
                const startDate = $('#event-start-date').val();
                const startTime = $('#event-start-time').val();
                const endDate = $('#event-end-date').val();
                const endTime = $('#event-end-time').val();
                const info = $('#event-info').val();
                const category = $('#event-category').val();

                // Validate time inputs
                if (startTime && !endTime && endDate) {
                  alert('Please fill in the end time when you specify an end date.');
                  return;
                }
                if (!startTime && endTime) {
                  alert('Please fill in the start time when you specify an end time.');
                  return;
                }

                if (title) {
                  // Create new event object
                  const eventData = {
                    id: generateEventId(),
                    title: title,
                    start: startTime ? moment(startDate + 'T' + startTime).format() : moment(startDate).format(),
                    end: endDate ? (endTime ? moment(endDate + 'T' + endTime).format() : moment(endDate).format()) : null,
                    description: info,
                    location: location,
                    category: category
                  };

                  console.log('Adding saved location to calendar:', eventData);

                  // Add the event to the calendar
                  $('#calendar').fullCalendar('renderEvent', eventData, true);
                  console.log('Event rendered on calendar:', eventData);

                  // Save to local storage and sync with Supabase
                  if (window.itinerarySync && typeof window.itinerarySync.addEvent === 'function') {
                    // Use the itinerarySync module to add the event
                    window.itinerarySync.addEvent(eventData).then(updatedEvents => {
                      console.log('Event added using itinerarySync module:', eventData);
                      // Update the calendar with the updated events
                      calendarEvents = updatedEvents;
                      $('#calendar').fullCalendar('removeEvents');
                      $('#calendar').fullCalendar('addEventSource', updatedEvents);
                      $('#calendar').fullCalendar('refetchEvents');
                      markDaysWithEvents();
                    }).catch(error => {
                      console.error('Error adding event with itinerarySync:', error);
                      // Fallback to the original method
                      loadEventsFromLocalStorage().then(events => {
                        events.push(eventData);
                        saveEventsToLocalStorage(events); // Save events to local storage after adding
                      }).catch(fallbackError => {
                        console.error('Error in fallback event adding:', fallbackError);
                        // Fallback to just saving the new event
                        saveEventsToLocalStorage([eventData]);
                      });
                    });
                  } else {
                    // Fallback to the original method
                    loadEventsFromLocalStorage().then(events => {
                      events.push(eventData);
                      saveEventsToLocalStorage(events); // Save events to local storage after adding
                    }).catch(error => {
                      console.error('Error loading events:', error);
                      // Fallback to just saving the new event
                      saveEventsToLocalStorage([eventData]);
                    });
                  }

                  // Refresh the calendar to ensure the event is displayed
                  $('#calendar').fullCalendar('refetchEvents');

                  // Close modal and reset form
                  modal.style.display = "none";
                  $('#event-form')[0].reset();

                  // Unselect the date on the calendar
                  $('#calendar').fullCalendar('unselect');

                  // Navigate to the event date
                  $('#calendar').fullCalendar('gotoDate', moment(eventData.start));

                  // Show confirmation message
                  alert('Event added to calendar successfully!');
                }
              });
            });

          // View on Map button
          const viewOnMapBtn = $('<button></button>')
            .append('<i class="fas fa-map-marked-alt"></i>')
            .append('<span>View on Map</span>')
            .addClass('view-on-map-btn')
            .click(() => {
              window.location.href = `map.html?location=${encodeURIComponent(marker.name)}`;
            });

          // Get Directions button
          const directionsBtn = $('<button></button>')
            .append('<i class="fas fa-directions"></i>')
            .append('<span>Directions</span>')
            .addClass('directions-btn')
            .click(() => {
              window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(marker.name)}`, '_blank');
            });

          buttonContainer.append(addToCalendarBtn, viewOnMapBtn, directionsBtn);
          listItem.append(placeInfo, buttonContainer);
          placesList.append(listItem);
        });

        savedPlacesList.append(placesList);
      }

      // Call loadSavedPlaces after the calendar is initialized
      loadSavedPlaces();

      // Helper function to open the event details modal
      function openEventDetailsModal(event) {
        console.log('Opening event details modal for:', event);

        // Get the details modal elements
        const detailsModal = document.getElementById('event-details-modal');
        const detailsTitle = document.getElementById('detail-title');
        const detailsDate = document.getElementById('detail-date');
        const detailsTime = document.getElementById('detail-time');
        const detailsLocation = document.getElementById('detail-location');
        const detailsCategory = document.getElementById('detail-category');
        const detailsInfo = document.getElementById('detail-info');
        const timeRow = document.getElementById('detail-time-row');
        const locationRow = document.getElementById('detail-location-row');
        const categoryRow = document.getElementById('detail-category-row');
        const infoRow = document.getElementById('detail-info-row');
        const viewOnMapButton = document.getElementById('view-on-map-button');
        const getDirectionsButton = document.getElementById('get-directions-button');

        // Fill in the details
        detailsTitle.textContent = event.title;

        // Format date - handle both string and moment object formats
        const dateFormat = 'dddd, MMMM D, YYYY';
        const startDate = typeof event.start === 'string' ? moment(event.start) : moment(event.start);
        detailsDate.textContent = startDate.format(dateFormat);

        // Show/hide time based on whether it exists
        const hasTime = typeof event.start === 'string'
          ? event.start.includes('T')
          : event.start.hasTime && event.start.hasTime();

        if (hasTime) {
          timeRow.style.display = 'flex';
          const startTime = typeof event.start === 'string'
            ? moment(event.start).format('h:mm A')
            : moment(event.start).format('h:mm A');

          detailsTime.textContent = startTime;

          // Handle end time if it exists
          if (event.end) {
            const hasEndTime = typeof event.end === 'string'
              ? event.end.includes('T')
              : event.end.hasTime && event.end.hasTime();

            if (hasEndTime) {
              const endTime = typeof event.end === 'string'
                ? moment(event.end).format('h:mm A')
                : moment(event.end).format('h:mm A');

              detailsTime.textContent += ' - ' + endTime;
            }
          }
        } else {
          timeRow.style.display = 'none';
        }

        // Show/hide location based on whether it exists
        if (event.location) {
          locationRow.style.display = 'flex';
          detailsLocation.textContent = event.location;
          viewOnMapButton.style.display = 'flex';
          getDirectionsButton.style.display = 'flex';
        } else {
          locationRow.style.display = 'none';
          viewOnMapButton.style.display = 'none';
          getDirectionsButton.style.display = 'none';
        }

        // Show/hide category based on whether it exists
        if (event.category) {
          categoryRow.style.display = 'flex';
          // Capitalize first letter of category
          detailsCategory.textContent = event.category.charAt(0).toUpperCase() + event.category.slice(1);
        } else {
          categoryRow.style.display = 'none';
        }

        // Show/hide additional info based on whether it exists
        if (event.description) {
          infoRow.style.display = 'flex';
          detailsInfo.textContent = event.description;
        } else {
          infoRow.style.display = 'none';
        }

        // Set up the edit button
        const editButton = document.getElementById('edit-event-button');
        editButton.onclick = function() {
          // Close the details modal
          detailsModal.style.display = 'none';
          // Open the edit modal
          openEditEventModal(event);
        };

        // Set up the view on map button
        viewOnMapButton.onclick = function() {
          window.location.href = `map.html?location=${encodeURIComponent(event.location)}`;
        };

        // Set up the get directions button
        getDirectionsButton.onclick = function() {
          window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(event.location)}`, '_blank');
        };

        // Show the modal
        detailsModal.style.display = 'block';

        // Set up close button
        const closeBtn = detailsModal.querySelector('.close');
        closeBtn.onclick = function() {
          detailsModal.style.display = 'none';
        };

        // Close when clicking outside
        let mouseDownOnModal = false;
        $(detailsModal).on('mousedown', function(event) {
          if (event.target === detailsModal) {
            mouseDownOnModal = true;
          } else {
            mouseDownOnModal = false;
          }
        });

        $(detailsModal).on('mouseup', function(event) {
          if (mouseDownOnModal && event.target === detailsModal) {
            detailsModal.style.display = 'none';
          }
          mouseDownOnModal = false;
        });
      }

      // Helper function to open the edit event modal
      function openEditEventModal(event) {
        // Store a deep copy of the event to avoid reference issues
        currentEditingEvent = {
          id: event.id,
          title: event.title,
          start: event.start,
          end: event.end,
          description: event.description,
          location: event.location,
          category: event.category
        };

        // Update modal title and buttons
        modalTitle.textContent = "Edit Event";
        saveButton.querySelector('span').textContent = "Save Changes";
        deleteButton.style.display = "block";

        // Fill form with event data
        $('#event-title').val(event.title);
        $('#event-location').val(event.location || '');
        $('#event-start-date').val(moment(event.start).format('YYYY-MM-DD'));
        $('#event-start-time').val(event.start.includes('T') ? moment(event.start).format('HH:mm') : '');
        $('#event-end-date').val(event.end ? moment(event.end).format('YYYY-MM-DD') : '');
        $('#event-end-time').val(event.end && event.end.includes('T') ? moment(event.end).format('HH:mm') : '');
        $('#event-category').val(event.category || 'other');
        $('#event-info').val(event.description || '');

        // Show modal
        modal.style.display = "block";

        // Setup form submission handler
        setupEventFormSubmit(event);

        // Set up delete button
        deleteButton.onclick = function() {
            if (currentEditingEvent) {
              $('#calendar').fullCalendar('removeEvents', currentEditingEvent.id);

              // Use the itinerarySync module if available
              if (window.itinerarySync && typeof window.itinerarySync.deleteEvent === 'function') {
                console.log('Using itinerarySync module to delete event:', currentEditingEvent.id);
                window.itinerarySync.deleteEvent(currentEditingEvent.id).then(updatedEvents => {
                  console.log('Event deleted using itinerarySync module');
                  // Update the calendar with the updated events
                  calendarEvents = updatedEvents;
                  $('#calendar').fullCalendar('removeEvents');
                  $('#calendar').fullCalendar('addEventSource', updatedEvents);
                  $('#calendar').fullCalendar('refetchEvents');
                  markDaysWithEvents();

                  modal.style.display = "none";
                  $('#event-form')[0].reset(); // Reset the form
                  currentEditingEvent = null; // Clear the current editing event
                }).catch(error => {
                  console.error('Error deleting event with itinerarySync:', error);
                  // Fallback to the original method
                  loadEventsFromLocalStorage().then(events => {
                    const eventIndex = events.findIndex(e => e.id === currentEditingEvent.id);
                    if (eventIndex !== -1) {
                      events.splice(eventIndex, 1);
                      saveEventsToLocalStorage(events); // Save events to local storage after deleting
                    }
                    modal.style.display = "none";
                    $('#event-form')[0].reset(); // Reset the form
                    currentEditingEvent = null; // Clear the current editing event
                  }).catch(fallbackError => {
                    console.error('Error in fallback event deletion:', fallbackError);
                    modal.style.display = "none";
                    $('#event-form')[0].reset(); // Reset the form anyway
                    currentEditingEvent = null; // Clear the current editing event
                  });
                });
              } else {
                // Fallback to the original method
                loadEventsFromLocalStorage().then(events => {
                  const eventIndex = events.findIndex(e => e.id === currentEditingEvent.id);
                  if (eventIndex !== -1) {
                    events.splice(eventIndex, 1);
                    saveEventsToLocalStorage(events); // Save events to local storage after deleting
                  }
                  modal.style.display = "none";
                  $('#event-form')[0].reset(); // Reset the form
                  currentEditingEvent = null; // Clear the current editing event
                }).catch(error => {
                  console.error('Error loading events for deletion:', error);
                  modal.style.display = "none";
                  $('#event-form')[0].reset(); // Reset the form anyway
                  currentEditingEvent = null; // Clear the current editing event
                });
              }
            }
          };
      }

      // Helper function to setup the event form submission
      function setupEventFormSubmit(originalEvent) {
        $('#event-form').off('submit').on('submit', function(e) {
          e.preventDefault();

          // Create updated event object
          const updatedEvent = {
            id: originalEvent.id,
            title: $('#event-title').val(),
            location: $('#event-location').val(),
            start: $('#event-start-time').val()
              ? moment($('#event-start-date').val() + 'T' + $('#event-start-time').val()).format()
              : moment($('#event-start-date').val()).format(),
            end: $('#event-end-date').val()
              ? ($('#event-end-time').val()
                ? moment($('#event-end-date').val() + 'T' + $('#event-end-time').val()).format()
                : moment($('#event-end-date').val()).format())
              : null,
            description: $('#event-info').val(),
            category: $('#event-category').val()
          };

          // Update the event in the calendar
          $('#calendar').fullCalendar('removeEvents', originalEvent.id);
          $('#calendar').fullCalendar('renderEvent', updatedEvent, true);

          // Update the event in local storage and sync with Supabase
          if (window.itinerarySync && typeof window.itinerarySync.updateEvent === 'function') {
            // Use the itinerarySync module to update the event
            console.log('Using itinerarySync module to update event:', updatedEvent);
            window.itinerarySync.updateEvent(updatedEvent).then(updatedEvents => {
              console.log('Event updated using itinerarySync module');
              // Update the calendar with the updated events
              calendarEvents = updatedEvents;
              $('#calendar').fullCalendar('removeEvents');
              $('#calendar').fullCalendar('addEventSource', updatedEvents);
              $('#calendar').fullCalendar('refetchEvents');
              markDaysWithEvents();

              modal.style.display = "none"; // Close the modal window
              $('#event-form')[0].reset(); // Reset the form
              currentEditingEvent = null; // Clear the current editing event
            }).catch(error => {
              console.error('Error updating event with itinerarySync:', error);
              // Fallback to the original method
              loadEventsFromLocalStorage().then(events => {
                const eventIndex = events.findIndex(e => e.id === originalEvent.id);
                if (eventIndex !== -1) {
                  events[eventIndex] = updatedEvent;
                  saveEventsToLocalStorage(events); // Save events to local storage after updating
                }
                modal.style.display = "none"; // Close the modal window
                $('#event-form')[0].reset(); // Reset the form
                currentEditingEvent = null; // Clear the current editing event
              }).catch(fallbackError => {
                console.error('Error in fallback event update:', fallbackError);
                modal.style.display = "none";
                $('#event-form')[0].reset();
                currentEditingEvent = null;
              });
            });
          } else {
            // Fallback to the original method
            loadEventsFromLocalStorage().then(events => {
              const eventIndex = events.findIndex(e => e.id === originalEvent.id);
              if (eventIndex !== -1) {
                events[eventIndex] = updatedEvent;
                saveEventsToLocalStorage(events); // Save events to local storage after updating
              }
              modal.style.display = "none"; // Close the modal window
              $('#event-form')[0].reset(); // Reset the form
              currentEditingEvent = null; // Clear the current editing event
            }).catch(error => {
              console.error('Error loading events for update:', error);
              // Still close the modal and reset the form
              modal.style.display = "none";
              $('#event-form')[0].reset();
              currentEditingEvent = null;
            });
          }
        });
      }

      // Add CSS for place coordinates
      const styles = `
        <style>
          .place-coords {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #aaa;
            font-size: 12px;
            margin-top: 5px;
          }

          .place-coords i {
            color: #ffd700;
            font-size: 14px;
          }

          .no-places {
            color: #aaa;
            text-align: center;
            padding: 20px;
            font-style: italic;
          }
        </style>
      `;
      $('head').append(styles);

      // Call markDaysWithEvents after calendar is initialized
      markDaysWithEvents();
      }

      // Initialize the calendar
      initializeCalendar();

      // Function to sync events with Supabase
      async function syncEventsWithSupabase(events) {
        console.log('Syncing events with Supabase...');
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            console.log('User not logged in, skipping Supabase sync');
            return { success: false, error: 'User not logged in' };
          }

          // Use the itineraryAPI client if available
          if (window.itineraryAPI && typeof window.itineraryAPI.syncUserEvents === 'function') {
            console.log('Using itineraryAPI to sync events');
            const result = await window.itineraryAPI.syncUserEvents(user.id, events);
            return { success: true, result };
          } else {
            console.warn('itineraryAPI not available, using fallback method');
            // Fallback implementation
            const response = await fetch('/.netlify/functions/sync-user-events', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                user_id: user.id,
                events: events
              })
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return { success: true, result };
          }
        } catch (error) {
          console.error('Error syncing events with Supabase:', error);
          return { success: false, error: error.message };
        }
      }

      // Function to load events from Supabase
      async function loadEventsFromSupabase() {
        console.log('Loading events from Supabase...');
        try {
          if (typeof supabase === 'undefined' || !supabase) {
            console.log('Supabase not available, cannot load events from Supabase');
            return [];
          }
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            console.log('User not logged in, cannot load events from Supabase');
            return [];
          }

          // Use the itineraryAPI client if available
          if (window.itineraryAPI && typeof window.itineraryAPI.getUserEvents === 'function') {
            console.log('Using itineraryAPI to get user events');
            return await window.itineraryAPI.getUserEvents(user.id);
          } else {
            console.warn('itineraryAPI not available, using fallback method');
            // Fallback implementation
            const response = await fetch(`/.netlify/functions/get-user-events?user_id=${encodeURIComponent(user.id)}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return result.events || [];
          }
        } catch (error) {
          console.error('Error loading events from Supabase:', error);
          return [];
        }
      }

      // Function to merge local and remote events
      function mergeEvents(localEvents, remoteEvents) {
        console.log('Merging local and remote events...');
        // Create a map of event IDs to events
        const eventMap = {};

        // Add all local events to the map
        localEvents.forEach(event => {
          eventMap[event.id] = { ...event, _source: 'local' };
        });

        // Add or update with remote events (remote takes precedence)
        remoteEvents.forEach(event => {
          // If the event already exists, check which one is newer
          if (eventMap[event.id]) {
            const localEvent = eventMap[event.id];
            const localUpdated = localEvent.updated_at ? new Date(localEvent.updated_at) : new Date(0);
            const remoteUpdated = event.updated_at ? new Date(event.updated_at) : new Date(0);

            // If remote is newer or equal, it takes precedence
            if (remoteUpdated >= localUpdated) {
              eventMap[event.id] = { ...event, _source: 'remote' };
            }
          } else {
            // If the event doesn't exist locally, add it
            eventMap[event.id] = { ...event, _source: 'remote' };
          }
        });

        // Convert the map back to an array
        const mergedEvents = Object.values(eventMap);
        console.log(`Merged ${localEvents.length} local and ${remoteEvents.length} remote events into ${mergedEvents.length} events`);

        // Remove the _source property
        return mergedEvents.map(event => {
          const { _source, ...cleanEvent } = event;
          return cleanEvent;
        });
      }

      // Load events using the new sync system
      console.log('Loading events using itinerary sync system...');

      // Wait a bit for the sync modules to load
      setTimeout(async () => {
        try {
          if (window.itinerarySync && typeof window.itinerarySync.initEventSync === 'function') {
            console.log('Using itinerarySync module to initialize events');
            const events = await window.itinerarySync.initEventSync();
            console.log('Events loaded via itinerarySync:', events.length);

            calendarEvents = events;
            $('#calendar').fullCalendar('removeEvents');
            $('#calendar').fullCalendar('addEventSource', events);
            $('#calendar').fullCalendar('refetchEvents');
            markDaysWithEvents();
          } else {
            console.log('itinerarySync not available, using fallback method');
            // Fallback to the original method
            const events = await loadEventsFromLocalStorage();
            calendarEvents = events;
            $('#calendar').fullCalendar('removeEvents');
            $('#calendar').fullCalendar('addEventSource', events);
            $('#calendar').fullCalendar('refetchEvents');
            markDaysWithEvents();
          }
        } catch (error) {
          console.error('Error loading events:', error);
          // Final fallback to direct local storage access
          try {
            const eventsJson = localStorage.getItem('events');
            const localEvents = JSON.parse(eventsJson) || [];
            calendarEvents = localEvents;
            $('#calendar').fullCalendar('removeEvents');
            $('#calendar').fullCalendar('addEventSource', localEvents);
            $('#calendar').fullCalendar('refetchEvents');
            markDaysWithEvents();
          } catch (localError) {
            console.error('Error loading events from local storage:', localError);
          }
        }
      }, 1000); // Wait 1 second for modules to load
    });
  </script>

  <!-- Authentication initialization -->
  <script>
    // Initialize auth when the page loads
    document.addEventListener('DOMContentLoaded', async function() {
      console.log('Itinerary page loaded, initializing auth...');

      // Initialize the itinerarySync module if available
      if (window.itinerarySync && typeof window.itinerarySync.init === 'function') {
        console.log('Initializing itinerarySync module...');
        try {
          await window.itinerarySync.init();
          console.log('itinerarySync module initialized successfully');
        } catch (error) {
          console.error('Error initializing itinerarySync module:', error);
        }
      } else {
        console.warn('itinerarySync module not available, using fallback methods');
      }

      // Wait for auth to initialize
      let user = null;
      if (typeof initAuth === 'function') {
        user = await initAuth();
      } else {
        console.warn('initAuth function not available, checking auth manually');
        try {
          if (typeof supabase !== 'undefined' && supabase) {
            const { data: { session } } = await supabase.auth.getSession();
            user = session?.user || null;
          } else {
            console.warn('Supabase not available for manual auth check');
          }
        } catch (error) {
          console.error('Error checking auth session:', error);
        }
      }

      if (user) {
        console.log('User authenticated:', user.email);
        // Force a sync of events now that we're authenticated
        if (window.itinerarySync && typeof window.itinerarySync.initEventSync === 'function') {
          console.log('Using itinerarySync module to load and sync events after authentication');
          window.itinerarySync.initEventSync().then(events => {
            console.log('Events loaded and synced after auth:', events.length);
            // Update the calendar with the synced events
            calendarEvents = events;
            $('#calendar').fullCalendar('removeEvents');
            $('#calendar').fullCalendar('addEventSource', events);
            $('#calendar').fullCalendar('refetchEvents');
            markDaysWithEvents();
          }).catch(error => {
            console.error('Error syncing events after auth:', error);
          });
        } else {
          // Fallback to the original method
          loadEventsFromLocalStorage().then(events => {
            console.log('Events loaded after auth:', events.length);
            // This will trigger a sync with Supabase if needed
            saveEventsToLocalStorage(events);
          });
        }
      } else {
        console.log('No authenticated user, using local storage only');
      }

      // Set up logout button if it exists
      const logoutButtons = document.querySelectorAll('.logout-btn');
      logoutButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
          e.preventDefault();
          if (typeof auth !== 'undefined') {
            auth.logout();
          } else {
            // Fallback logout
            if (typeof supabase !== 'undefined' && supabase) {
              supabase.auth.signOut();
            }
          }
          // Redirect to home page after logout
          window.location.href = 'index.html';
        });
      });


    });
  </script>
</body>
</html>